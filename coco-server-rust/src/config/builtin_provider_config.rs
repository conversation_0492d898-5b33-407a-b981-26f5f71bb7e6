use std::{collections::HashMap, path::Path};

use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use tracing::{debug, error, info, warn};
use uuid::Uuid;

use crate::{
    config::toml_parser::{BuiltinProviderConfig, ProviderConfig, TomlParser},
    error::{error::CocoError, result::Result},
    models::model_provider::ModelProvider,
};

/// 内置提供商配置管理器
/// 负责管理内置模型提供商的配置，包括加载、验证、转换等功能
#[derive(Debug, Clone)]
pub struct BuiltinProviderConfigManager {
    /// TOML解析器
    toml_parser: TomlParser,
    /// 配置版本
    config_version: Option<String>,
    /// 最后加载时间
    last_loaded: Option<DateTime<Utc>>,
    /// 配置缓存
    cached_providers: HashMap<String, ModelProvider>,
}

/// 配置导入策略
#[derive(Debug, Clone, PartialEq)]
pub enum ImportStrategy {
    /// 跳过已存在的提供商
    SkipExisting,
    /// 更新已存在的提供商（保留用户敏感字段）
    UpdateExisting,
    /// 强制覆盖所有字段
    ForceOverwrite,
}

/// 配置导入结果
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ImportResult {
    /// 成功导入的提供商数量
    pub imported_count: usize,
    /// 跳过的提供商数量
    pub skipped_count: usize,
    /// 更新的提供商数量
    pub updated_count: usize,
    /// 失败的提供商数量
    pub failed_count: usize,
    /// 导入详情
    pub details: Vec<ImportDetail>,
    /// 导入耗时（毫秒）
    pub duration_ms: u64,
}

/// 导入详情
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ImportDetail {
    /// 提供商ID
    pub provider_id: String,
    /// 操作类型
    pub action: ImportAction,
    /// 是否成功
    pub success: bool,
    /// 错误信息（如果失败）
    pub error: Option<String>,
}

/// 导入操作类型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ImportAction {
    /// 新建
    Created,
    /// 更新
    Updated,
    /// 跳过
    Skipped,
    /// 失败
    Failed,
}

impl BuiltinProviderConfigManager {
    /// 创建新的内置提供商配置管理器
    pub fn new<P: AsRef<Path>>(config_path: P) -> Self {
        let toml_parser = TomlParser::new(config_path);

        Self {
            toml_parser,
            config_version: None,
            last_loaded: None,
            cached_providers: HashMap::new(),
        }
    }

    /// 加载配置文件
    pub fn load_config(&mut self) -> Result<&BuiltinProviderConfig> {
        info!("开始加载内置提供商配置");

        let config = self.toml_parser.parse()?;
        self.config_version = Some(config.metadata.version.clone());
        self.last_loaded = Some(Utc::now());

        // 清空缓存
        self.cached_providers.clear();

        info!("内置提供商配置加载成功，版本: {}", config.metadata.version);
        Ok(config)
    }

    /// 重新加载配置文件
    pub fn reload_config(&mut self) -> Result<&BuiltinProviderConfig> {
        info!("重新加载内置提供商配置");

        let config = self.toml_parser.reload()?;
        self.config_version = Some(config.metadata.version.clone());
        self.last_loaded = Some(Utc::now());

        // 清空缓存
        self.cached_providers.clear();

        info!("内置提供商配置重新加载成功");
        Ok(config)
    }

    /// 获取当前配置
    pub fn get_config(&self) -> Option<&BuiltinProviderConfig> {
        self.toml_parser.get_config()
    }

    /// 获取配置版本
    pub fn get_config_version(&self) -> Option<&String> {
        self.config_version.as_ref()
    }

    /// 获取最后加载时间
    pub fn get_last_loaded(&self) -> Option<DateTime<Utc>> {
        self.last_loaded
    }

    /// 将TOML配置转换为ModelProvider
    pub fn convert_to_model_providers(&mut self) -> Result<Vec<ModelProvider>> {
        // 先获取配置的克隆，避免借用冲突
        let config = self
            .get_config()
            .ok_or_else(|| CocoError::ConfigError("配置未加载".to_string()))?
            .clone();

        let mut providers = Vec::new();

        for provider_config in &config.providers {
            match self.convert_provider_config(provider_config) {
                Ok(provider) => {
                    // 使用配置中的 ID 作为缓存键
                    self.cached_providers
                        .insert(provider_config.id.clone(), provider.clone());
                    providers.push(provider);
                }
                Err(e) => {
                    error!("转换提供商配置失败 {}: {}", provider_config.id, e);
                    // 继续处理其他提供商，不因单个失败而中断
                }
            }
        }

        info!("成功转换 {} 个内置提供商配置", providers.len());
        Ok(providers)
    }

    /// 转换单个提供商配置
    fn convert_provider_config(&self, config: &ProviderConfig) -> Result<ModelProvider> {
        let now = Utc::now();

        // 转换模型列表
        let models: Vec<crate::models::model_provider::ModelConfig> = config
            .models
            .iter()
            .map(|m| crate::models::model_provider::ModelConfig {
                name: m.name.clone(),
                settings: None,
            })
            .collect();

        let provider = ModelProvider {
            id: None, // SurrealDB 会自动生成 ID
            name: config.name.clone(),
            api_key: config.api_key.clone(),
            api_type: config.api_type.clone(),
            base_url: config.base_url.clone(),
            icon: config.icon.clone(),
            enabled: config.enabled,
            builtin: config.builtin,
            description: config.description.clone(),
            models,
            created: now,
            updated: now,
        };

        debug!("转换提供商配置: {} -> {:?}", config.id, provider.id);
        Ok(provider)
    }

    /// 根据ID获取缓存的提供商
    pub fn get_cached_provider(&self, id: &str) -> Option<&ModelProvider> {
        self.cached_providers.get(id)
    }

    /// 获取所有缓存的提供商
    pub fn get_all_cached_providers(&self) -> Vec<&ModelProvider> {
        self.cached_providers.values().collect()
    }

    /// 清空缓存
    pub fn clear_cache(&mut self) {
        self.cached_providers.clear();
        info!("内置提供商缓存已清空");
    }

    /// 验证配置完整性
    pub fn validate_config(&self) -> Result<()> {
        let config = self
            .get_config()
            .ok_or_else(|| CocoError::ConfigError("配置未加载".to_string()))?;

        // 验证元数据
        if config.metadata.version.is_empty() {
            return Err(CocoError::ConfigError("配置版本不能为空".to_string()));
        }

        // 验证提供商配置
        for provider in &config.providers {
            self.validate_provider_config(provider)?;
        }

        info!("内置提供商配置验证通过");
        Ok(())
    }

    /// 验证单个提供商配置
    fn validate_provider_config(&self, provider: &ProviderConfig) -> Result<()> {
        if provider.id.is_empty() {
            return Err(CocoError::ConfigError("提供商ID不能为空".to_string()));
        }

        if provider.name.is_empty() {
            return Err(CocoError::ConfigError(format!(
                "提供商 {} 的名称不能为空",
                provider.id
            )));
        }

        if provider.api_type.is_empty() {
            return Err(CocoError::ConfigError(format!(
                "提供商 {} 的API类型不能为空",
                provider.id
            )));
        }

        // 验证URL格式（如果不为空）
        if !provider.base_url.is_empty() {
            if let Err(_) = url::Url::parse(&provider.base_url) {
                warn!(
                    "提供商 {} 的base_url格式可能不正确: {}",
                    provider.id, provider.base_url
                );
            }
        }

        Ok(())
    }

    /// 获取配置统计信息
    pub fn get_config_stats(&self) -> Option<ConfigStats> {
        let config = self.get_config()?;

        let total_providers = config.providers.len();
        let builtin_providers = config.providers.iter().filter(|p| p.builtin).count();
        let enabled_providers = config.providers.iter().filter(|p| p.enabled).count();
        let total_models = config.providers.iter().map(|p| p.models.len()).sum();

        Some(ConfigStats {
            total_providers,
            builtin_providers,
            enabled_providers,
            total_models,
            config_version: config.metadata.version.clone(),
            last_updated: config.metadata.last_updated.clone(),
        })
    }
}

/// 配置统计信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ConfigStats {
    /// 总提供商数量
    pub total_providers: usize,
    /// 内置提供商数量
    pub builtin_providers: usize,
    /// 启用的提供商数量
    pub enabled_providers: usize,
    /// 总模型数量
    pub total_models: usize,
    /// 配置版本
    pub config_version: String,
    /// 最后更新时间
    pub last_updated: String,
}

#[cfg(test)]
mod tests {
    use std::fs;

    use tempfile::NamedTempFile;

    use super::*;

    fn create_test_config() -> String {
        r#"
[metadata]
version = "1.0.0"
description = "测试配置"
last_updated = "2025-01-15T10:30:00Z"

[[providers]]
id = "openai"
name = "OpenAI"
api_key = ""
api_type = "openai"
base_url = "https://api.openai.com"
icon = "/assets/icons/llm/openai.svg"
enabled = false
builtin = true
description = "OpenAI提供商"

[[providers.models]]
name = "gpt-4"

[[providers.models]]
name = "gpt-3.5-turbo"
"#
        .to_string()
    }

    #[test]
    fn test_load_config() {
        let toml_content = create_test_config();
        let temp_file = NamedTempFile::new().unwrap();
        fs::write(temp_file.path(), toml_content).unwrap();

        let mut manager = BuiltinProviderConfigManager::new(temp_file.path());
        let config = manager.load_config().unwrap();

        assert_eq!(config.metadata.version, "1.0.0");
        assert_eq!(config.providers.len(), 1);
        assert_eq!(manager.get_config_version(), Some(&"1.0.0".to_string()));
    }

    #[test]
    fn test_convert_to_model_providers() {
        let toml_content = create_test_config();
        let temp_file = NamedTempFile::new().unwrap();
        fs::write(temp_file.path(), toml_content).unwrap();

        let mut manager = BuiltinProviderConfigManager::new(temp_file.path());
        manager.load_config().unwrap();

        let providers = manager.convert_to_model_providers().unwrap();
        assert_eq!(providers.len(), 1);

        let provider = &providers[0];
        assert_eq!(provider.name, "OpenAI");
        assert_eq!(provider.api_type, "openai");
        assert_eq!(provider.builtin, true);
        assert!(!provider.models.is_empty());
    }
}
