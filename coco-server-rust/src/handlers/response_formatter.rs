use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use serde_json::Value;

use crate::{
    handlers::field_filter::{FieldFilter, FilteredModelProvider},
    models::model_provider::ModelProvider,
};

/// 模型提供商API响应格式化器
///
/// 负责将业务数据转换为符合API规格的响应格式
pub struct ResponseFormatter;

/// 创建模型提供商响应
#[derive(Debug, Serialize, Deserialize)]
pub struct CreateModelProviderResponse {
    #[serde(rename = "_id")]
    pub id: String,
    pub result: String,
}

/// 获取模型提供商响应
#[derive(Debug, Serialize, Deserialize)]
pub struct GetModelProviderResponse {
    #[serde(rename = "_id")]
    pub id: String,
    pub found: bool,
    #[serde(rename = "_source", skip_serializing_if = "Option::is_none")]
    pub source: Option<ModelProviderSource>,
}

/// 模型提供商源数据（不包含敏感信息）
#[derive(Debug, Serialize, Deserialize)]
pub struct ModelProviderSource {
    pub id: String,
    pub created: chrono::DateTime<chrono::Utc>,
    pub updated: chrono::DateTime<chrono::Utc>,
    pub name: String,
    pub api_type: String,
    pub base_url: String,
    pub icon: String,
    pub models: Vec<Value>,
    pub enabled: bool,
    pub builtin: bool,
    pub description: String,
    // 注意：不包含api_key等敏感字段
}

/// 更新模型提供商响应
#[derive(Debug, Serialize, Deserialize)]
pub struct UpdateModelProviderResponse {
    #[serde(rename = "_id")]
    pub id: String,
    pub result: String,
}

/// 删除模型提供商响应
#[derive(Debug, Serialize, Deserialize)]
pub struct DeleteModelProviderResponse {
    #[serde(rename = "_id")]
    pub id: String,
    pub result: String,
}

/// 搜索响应
#[derive(Debug, Serialize, Deserialize)]
pub struct SearchModelProviderResponse {
    pub took: u64,
    pub timed_out: bool,
    pub hits: SearchHits,
}

/// 搜索结果集合
#[derive(Debug, Serialize, Deserialize)]
pub struct SearchHits {
    pub total: SearchTotal,
    pub hits: Vec<SearchHit>,
}

/// 搜索总数信息
#[derive(Debug, Serialize, Deserialize)]
pub struct SearchTotal {
    pub value: usize,
    pub relation: String, // "eq" 表示精确匹配
}

/// 搜索结果项
#[derive(Debug, Serialize, Deserialize)]
pub struct SearchHit {
    #[serde(rename = "_id")]
    pub id: String,
    #[serde(rename = "_source")]
    pub source: FilteredModelProvider,
}

/// 增强的错误响应（符合API规格）
#[derive(Debug, Serialize, Deserialize)]
pub struct ErrorResponse {
    pub error: ErrorInfo,
    pub timestamp: DateTime<Utc>,
    pub path: String,
}

/// 错误信息
#[derive(Debug, Serialize, Deserialize)]
pub struct ErrorInfo {
    #[serde(rename = "type")]
    pub error_type: String,
    pub message: String,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub details: Option<Vec<ErrorDetail>>,
}

/// 错误详情
#[derive(Debug, Serialize, Deserialize)]
pub struct ErrorDetail {
    pub field: String,
    pub message: String,
}

/// 简单错误响应（向后兼容）
#[derive(Debug, Serialize, Deserialize)]
pub struct SimpleErrorResponse {
    pub error: String,
    pub message: String,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub details: Option<Value>,
}

impl ResponseFormatter {
    /// 格式化创建成功响应
    ///
    /// # 参数
    /// * `id` - 创建的模型提供商ID
    pub fn format_create_success(id: String) -> CreateModelProviderResponse {
        CreateModelProviderResponse {
            id,
            result: "created".to_string(),
        }
    }

    /// 格式化获取成功响应
    ///
    /// # 参数
    /// * `provider` - 模型提供商数据
    pub fn format_get_success(provider: ModelProvider) -> GetModelProviderResponse {
        let id_string = provider.get_id_string();

        let source = ModelProviderSource {
            id: id_string.clone(),
            created: provider.created,
            updated: provider.updated,
            name: provider.name,
            api_type: provider.api_type,
            base_url: provider.base_url,
            icon: provider.icon,
            models: provider
                .models
                .into_iter()
                .map(|model| serde_json::to_value(model).unwrap_or_default())
                .collect(),
            enabled: provider.enabled,
            builtin: provider.builtin,
            description: provider.description,
            // 故意不包含api_key
        };

        GetModelProviderResponse {
            id: id_string,
            found: true,
            source: Some(source),
        }
    }

    /// 格式化获取失败响应（未找到）
    ///
    /// # 参数
    /// * `id` - 请求的模型提供商ID
    pub fn format_get_not_found(id: String) -> GetModelProviderResponse {
        GetModelProviderResponse {
            id,
            found: false,
            source: None,
        }
    }

    /// 格式化更新成功响应
    ///
    /// # 参数
    /// * `id` - 更新的模型提供商ID
    pub fn format_update_success(id: String) -> UpdateModelProviderResponse {
        UpdateModelProviderResponse {
            id,
            result: "updated".to_string(),
        }
    }

    /// 格式化删除成功响应
    ///
    /// # 参数
    /// * `id` - 删除的模型提供商ID
    pub fn format_delete_success(id: String) -> DeleteModelProviderResponse {
        DeleteModelProviderResponse {
            id,
            result: "deleted".to_string(),
        }
    }

    /// 格式化搜索响应
    ///
    /// # 参数
    /// * `providers` - 搜索到的模型提供商列表
    /// * `total` - 总数
    /// * `took` - 搜索耗时（毫秒）
    pub fn format_search_success(
        providers: Vec<ModelProvider>,
        total: usize,
        took: u64,
    ) -> SearchModelProviderResponse {
        let hits = providers
            .into_iter()
            .map(|provider| {
                let id = provider.get_id_string();
                SearchHit {
                    id,
                    source: FieldFilter::filter_sensitive_fields(provider),
                }
            })
            .collect();

        SearchModelProviderResponse {
            took,
            timed_out: false,
            hits: SearchHits {
                total: SearchTotal {
                    value: total,
                    relation: "eq".to_string(),
                },
                hits,
            },
        }
    }

    /// 格式化错误响应（新版本，符合API规格）
    ///
    /// # 参数
    /// * `error_type` - 错误类型
    /// * `message` - 错误消息
    /// * `path` - 请求路径
    /// * `details` - 可选的错误详情
    pub fn format_error_v2(
        error_type: &str,
        message: &str,
        path: &str,
        details: Option<Vec<ErrorDetail>>,
    ) -> ErrorResponse {
        ErrorResponse {
            error: ErrorInfo {
                error_type: error_type.to_string(),
                message: message.to_string(),
                details,
            },
            timestamp: Utc::now(),
            path: path.to_string(),
        }
    }

    /// 格式化错误响应（旧版本，保持向后兼容）
    ///
    /// # 参数
    /// * `error` - 错误类型
    /// * `message` - 错误消息
    /// * `details` - 可选的错误详情
    pub fn format_error(error: &str, message: &str, details: Option<Value>) -> SimpleErrorResponse {
        SimpleErrorResponse {
            error: error.to_string(),
            message: message.to_string(),
            details,
        }
    }

    /// 格式化验证错误响应
    ///
    /// # 参数
    /// * `message` - 验证错误消息
    /// * `path` - 请求路径
    /// * `field_errors` - 字段错误详情
    pub fn format_validation_error_v2(
        message: &str,
        path: &str,
        field_errors: Option<Vec<ErrorDetail>>,
    ) -> ErrorResponse {
        Self::format_error_v2("ValidationError", message, path, field_errors)
    }

    /// 格式化冲突错误响应
    ///
    /// # 参数
    /// * `message` - 冲突错误消息
    /// * `path` - 请求路径
    pub fn format_conflict_error_v2(message: &str, path: &str) -> ErrorResponse {
        Self::format_error_v2("ConflictError", message, path, None)
    }

    /// 格式化未找到错误响应
    ///
    /// # 参数
    /// * `message` - 未找到错误消息
    /// * `path` - 请求路径
    pub fn format_not_found_error_v2(message: &str, path: &str) -> ErrorResponse {
        Self::format_error_v2("NotFoundError", message, path, None)
    }

    /// 格式化内部服务器错误响应
    ///
    /// # 参数
    /// * `message` - 错误消息
    /// * `path` - 请求路径
    pub fn format_internal_error_v2(message: &str, path: &str) -> ErrorResponse {
        Self::format_error_v2("InternalError", message, path, None)
    }

    /// 格式化权限错误响应
    ///
    /// # 参数
    /// * `message` - 权限错误消息
    /// * `path` - 请求路径
    pub fn format_permission_error_v2(message: &str, path: &str) -> ErrorResponse {
        Self::format_error_v2("ForbiddenError", message, path, None)
    }

    // 保持向后兼容的旧方法
    pub fn format_validation_error(message: &str) -> SimpleErrorResponse {
        Self::format_error("validation_error", message, None)
    }

    pub fn format_conflict_error(message: &str) -> SimpleErrorResponse {
        Self::format_error("conflict", message, None)
    }

    pub fn format_not_found_error(message: &str) -> SimpleErrorResponse {
        Self::format_error("not_found", message, None)
    }

    pub fn format_internal_error(message: &str) -> SimpleErrorResponse {
        Self::format_error("internal_error", message, None)
    }

    pub fn format_permission_error(message: &str) -> SimpleErrorResponse {
        Self::format_error("permission_denied", message, None)
    }
}

#[cfg(test)]
mod tests {
    use chrono::Utc;

    use super::*;

    fn create_test_provider() -> ModelProvider {
        ModelProvider {
            id: "test-id".to_string(),
            created: Utc::now(),
            updated: Utc::now(),
            name: "Test Provider".to_string(),
            api_key: "secret-key".to_string(),
            api_type: "openai".to_string(),
            base_url: "https://api.test.com".to_string(),
            icon: "test-icon".to_string(),
            models: vec![],
            enabled: true,
            builtin: false,
            description: "Test description".to_string(),
        }
    }

    #[test]
    fn test_format_create_success() {
        let response = ResponseFormatter::format_create_success("test-id".to_string());
        assert_eq!(response.id, "test-id");
        assert_eq!(response.result, "created");
    }

    #[test]
    fn test_format_get_success() {
        let provider = create_test_provider();
        let response = ResponseFormatter::format_get_success(provider);

        assert_eq!(response.id, "test-id");
        assert!(response.found);
        assert!(response.source.is_some());

        let source = response.source.unwrap();
        assert_eq!(source.name, "Test Provider");
        assert_eq!(source.api_type, "openai");
        // 确保不包含敏感信息
        // api_key字段不应该在source中
    }

    #[test]
    fn test_format_get_not_found() {
        let response = ResponseFormatter::format_get_not_found("test-id".to_string());
        assert_eq!(response.id, "test-id");
        assert!(!response.found);
        assert!(response.source.is_none());
    }

    #[test]
    fn test_format_error() {
        let response = ResponseFormatter::format_validation_error("Invalid input");
        assert_eq!(response.error, "validation_error");
        assert_eq!(response.message, "Invalid input");
        assert!(response.details.is_none());
    }

    #[test]
    fn test_format_search_success() {
        let provider = create_test_provider();
        let providers = vec![provider];
        let response = ResponseFormatter::format_search_success(providers, 1, 15);

        assert_eq!(response.took, 15);
        assert!(!response.timed_out);
        assert_eq!(response.hits.total.value, 1);
        assert_eq!(response.hits.total.relation, "eq");
        assert_eq!(response.hits.hits.len(), 1);

        let hit = &response.hits.hits[0];
        assert_eq!(hit.id, "test-id");
        assert_eq!(hit.source.name, "Test Provider");
        assert_eq!(hit.source.api_type, "openai");
        // 确保敏感字段被过滤
        // api_key字段不应该在source中
    }

    #[test]
    fn test_format_error_v2() {
        let details = vec![ErrorDetail {
            field: "name".to_string(),
            message: "名称不能为空".to_string(),
        }];

        let response = ResponseFormatter::format_error_v2(
            "ValidationError",
            "请求参数验证失败",
            "/model_provider/",
            Some(details),
        );

        assert_eq!(response.error.error_type, "ValidationError");
        assert_eq!(response.error.message, "请求参数验证失败");
        assert_eq!(response.path, "/model_provider/");
        assert!(response.error.details.is_some());

        let details = response.error.details.unwrap();
        assert_eq!(details.len(), 1);
        assert_eq!(details[0].field, "name");
        assert_eq!(details[0].message, "名称不能为空");
    }

    #[test]
    fn test_format_validation_error_v2() {
        let field_errors = vec![
            ErrorDetail {
                field: "name".to_string(),
                message: "名称不能为空".to_string(),
            },
            ErrorDetail {
                field: "api_key".to_string(),
                message: "API密钥不能为空".to_string(),
            },
        ];

        let response = ResponseFormatter::format_validation_error_v2(
            "请求参数验证失败",
            "/model_provider/",
            Some(field_errors),
        );

        assert_eq!(response.error.error_type, "ValidationError");
        assert_eq!(response.error.message, "请求参数验证失败");
        assert_eq!(response.path, "/model_provider/");

        let details = response.error.details.unwrap();
        assert_eq!(details.len(), 2);
        assert_eq!(details[0].field, "name");
        assert_eq!(details[1].field, "api_key");
    }

    #[test]
    fn test_format_conflict_error_v2() {
        let response =
            ResponseFormatter::format_conflict_error_v2("模型提供商名称已存在", "/model_provider/");

        assert_eq!(response.error.error_type, "ConflictError");
        assert_eq!(response.error.message, "模型提供商名称已存在");
        assert_eq!(response.path, "/model_provider/");
        assert!(response.error.details.is_none());
    }

    #[test]
    fn test_format_not_found_error_v2() {
        let response = ResponseFormatter::format_not_found_error_v2(
            "模型提供商不存在",
            "/model_provider/test-id",
        );

        assert_eq!(response.error.error_type, "NotFoundError");
        assert_eq!(response.error.message, "模型提供商不存在");
        assert_eq!(response.path, "/model_provider/test-id");
    }

    #[test]
    fn test_format_permission_error_v2() {
        let response = ResponseFormatter::format_permission_error_v2(
            "不能删除内置提供商",
            "/model_provider/builtin-id",
        );

        assert_eq!(response.error.error_type, "ForbiddenError");
        assert_eq!(response.error.message, "不能删除内置提供商");
        assert_eq!(response.path, "/model_provider/builtin-id");
    }
}
