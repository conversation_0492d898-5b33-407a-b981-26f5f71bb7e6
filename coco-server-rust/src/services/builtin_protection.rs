use tracing::{debug, warn};

use crate::{
    error::error::CocoError,
    models::model_provider::{ModelProvider, UpdateModelProviderRequest},
};

/// 内置提供商保护服务
///
/// 负责实现内置模型提供商的保护逻辑，防止误操作：
/// 1. 禁止删除内置提供商
/// 2. 禁止修改内置提供商的关键字段（name, builtin, created）
/// 3. 提供详细的错误信息和日志记录
#[derive(Debug, Clone)]
pub struct BuiltinProtectionService;

impl BuiltinProtectionService {
    /// 创建新的内置提供商保护服务实例
    pub fn new() -> Self {
        Self
    }

    /// 验证删除操作是否被允许
    ///
    /// # 参数
    /// * `provider` - 要删除的模型提供商
    ///
    /// # 返回
    /// * `Ok(())` - 删除操作被允许
    /// * `Err(CocoError)` - 删除操作被禁止，包含具体错误信息
    pub fn validate_delete_operation(&self, provider: &ModelProvider) -> Result<(), CocoError> {
        if provider.builtin {
            warn!(
                "尝试删除内置模型提供商被阻止: id={}, name={}",
                provider.get_id_string(),
                provider.name
            );
            return Err(CocoError::builtin_provider_protection(
                "内置模型提供商不能被删除",
            ));
        }

        debug!(
            "删除操作验证通过: id={}, name={}",
            provider.get_id_string(),
            provider.name
        );
        Ok(())
    }

    /// 验证更新操作是否被允许
    ///
    /// # 参数
    /// * `existing_provider` - 现有的模型提供商
    /// * `update_request` - 更新请求
    ///
    /// # 返回
    /// * `Ok(())` - 更新操作被允许
    /// * `Err(CocoError)` - 更新操作被禁止，包含具体错误信息
    pub fn validate_update_operation(
        &self,
        existing_provider: &ModelProvider,
        update_request: &UpdateModelProviderRequest,
    ) -> Result<(), CocoError> {
        if !existing_provider.builtin {
            // 非内置提供商，允许所有更新操作
            debug!(
                "非内置提供商更新验证通过: id={}, name={}",
                existing_provider.get_id_string(),
                existing_provider.name
            );
            return Ok(());
        }

        // 内置提供商的保护检查
        self.validate_name_protection(existing_provider, update_request)?;
        self.validate_builtin_field_protection(existing_provider, update_request)?;
        self.validate_created_field_protection(existing_provider, update_request)?;

        debug!(
            "内置提供商更新验证通过: id={}, name={}",
            existing_provider.get_id_string(),
            existing_provider.name
        );
        Ok(())
    }

    /// 验证名称字段保护
    ///
    /// 内置提供商的名称不能被修改
    fn validate_name_protection(
        &self,
        existing_provider: &ModelProvider,
        update_request: &UpdateModelProviderRequest,
    ) -> Result<(), CocoError> {
        if let Some(ref new_name) = update_request.name {
            if new_name != &existing_provider.name {
                warn!(
                    "尝试修改内置提供商名称被阻止: id={}, 原名称={}, 新名称={}",
                    existing_provider.get_id_string(),
                    existing_provider.name,
                    new_name
                );
                return Err(CocoError::builtin_provider_protection(
                    "内置模型提供商的名称不能被修改",
                ));
            }
        }
        Ok(())
    }

    /// 验证builtin字段保护
    ///
    /// 注意：UpdateModelProviderRequest中不包含builtin字段，
    /// 这个方法主要用于防御性编程，防止未来的代码变更引入安全问题
    fn validate_builtin_field_protection(
        &self,
        existing_provider: &ModelProvider,
        _update_request: &UpdateModelProviderRequest,
    ) -> Result<(), CocoError> {
        // 由于UpdateModelProviderRequest设计上不包含builtin字段，
        // 这里主要是防御性检查，确保builtin字段不会被意外修改
        debug!(
            "Builtin字段保护检查通过: id={}, builtin={}",
            existing_provider.get_id_string(),
            existing_provider.builtin
        );
        Ok(())
    }

    /// 验证created字段保护
    ///
    /// 注意：UpdateModelProviderRequest中不包含created字段，
    /// 这个方法主要用于防御性编程，防止未来的代码变更引入安全问题
    fn validate_created_field_protection(
        &self,
        existing_provider: &ModelProvider,
        _update_request: &UpdateModelProviderRequest,
    ) -> Result<(), CocoError> {
        // 由于UpdateModelProviderRequest设计上不包含created字段，
        // 这里主要是防御性检查，确保created字段不会被意外修改
        debug!(
            "Created字段保护检查通过: id={}, created={}",
            existing_provider.get_id_string(),
            existing_provider.created
        );
        Ok(())
    }

    /// 检查提供商是否为内置提供商
    ///
    /// # 参数
    /// * `provider` - 要检查的模型提供商
    ///
    /// # 返回
    /// * `true` - 是内置提供商
    /// * `false` - 不是内置提供商
    pub fn is_builtin_provider(&self, provider: &ModelProvider) -> bool {
        provider.builtin
    }

    /// 获取保护规则说明
    ///
    /// 返回内置提供商保护规则的详细说明，用于文档和错误信息
    pub fn get_protection_rules(&self) -> Vec<&'static str> {
        vec![
            "内置模型提供商不能被删除",
            "内置模型提供商的名称不能被修改",
            "内置模型提供商的builtin字段不能被修改",
            "内置模型提供商的created字段不能被修改",
        ]
    }
}

impl Default for BuiltinProtectionService {
    fn default() -> Self {
        Self::new()
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::models::model_provider::CreateModelProviderRequest;

    /// 创建测试用的内置提供商
    fn create_builtin_provider() -> ModelProvider {
        let req = CreateModelProviderRequest {
            name: "Built-in Provider".to_string(),
            api_key: "test-key".to_string(),
            api_type: "openai".to_string(),
            base_url: "https://api.openai.com".to_string(),
            icon: "openai-icon".to_string(),
            models: vec![],
            enabled: true,
            description: "Built-in OpenAI provider".to_string(),
        };

        let mut provider = ModelProvider::new(req);
        provider.builtin = true; // 设置为内置提供商
        provider
    }

    /// 创建测试用的普通提供商
    fn create_regular_provider() -> ModelProvider {
        let req = CreateModelProviderRequest {
            name: "Regular Provider".to_string(),
            api_key: "test-key".to_string(),
            api_type: "custom".to_string(),
            base_url: "https://api.custom.com".to_string(),
            icon: "custom-icon".to_string(),
            models: vec![],
            enabled: true,
            description: "Regular custom provider".to_string(),
        };

        ModelProvider::new(req) // builtin默认为false
    }

    #[test]
    fn test_create_protection_service() {
        let service = BuiltinProtectionService::new();
        assert!(!service.get_protection_rules().is_empty());
    }

    #[test]
    fn test_is_builtin_provider() {
        let service = BuiltinProtectionService::new();
        let builtin_provider = create_builtin_provider();
        let regular_provider = create_regular_provider();

        assert!(service.is_builtin_provider(&builtin_provider));
        assert!(!service.is_builtin_provider(&regular_provider));
    }

    #[test]
    fn test_validate_delete_builtin_provider_forbidden() {
        let service = BuiltinProtectionService::new();
        let builtin_provider = create_builtin_provider();

        let result = service.validate_delete_operation(&builtin_provider);
        assert!(result.is_err());
        assert!(result
            .unwrap_err()
            .to_string()
            .contains("内置模型提供商不能被删除"));
    }

    #[test]
    fn test_validate_delete_regular_provider_allowed() {
        let service = BuiltinProtectionService::new();
        let regular_provider = create_regular_provider();

        let result = service.validate_delete_operation(&regular_provider);
        assert!(result.is_ok());
    }

    #[test]
    fn test_validate_update_builtin_provider_name_forbidden() {
        let service = BuiltinProtectionService::new();
        let builtin_provider = create_builtin_provider();

        let update_request = UpdateModelProviderRequest {
            name: Some("New Name".to_string()),
            api_key: None,
            api_type: None,
            base_url: None,
            icon: None,
            models: None,
            enabled: None,
            description: None,
        };

        let result = service.validate_update_operation(&builtin_provider, &update_request);
        assert!(result.is_err());
        assert!(result
            .unwrap_err()
            .to_string()
            .contains("内置模型提供商的名称不能被修改"));
    }

    #[test]
    fn test_validate_update_builtin_provider_same_name_allowed() {
        let service = BuiltinProtectionService::new();
        let builtin_provider = create_builtin_provider();

        let update_request = UpdateModelProviderRequest {
            name: Some(builtin_provider.name.clone()), // 相同的名称
            api_key: Some("new-key".to_string()),
            api_type: None,
            base_url: None,
            icon: None,
            models: None,
            enabled: Some(false),
            description: Some("Updated description".to_string()),
        };

        let result = service.validate_update_operation(&builtin_provider, &update_request);
        assert!(result.is_ok());
    }

    #[test]
    fn test_validate_update_regular_provider_allowed() {
        let service = BuiltinProtectionService::new();
        let regular_provider = create_regular_provider();

        let update_request = UpdateModelProviderRequest {
            name: Some("New Name".to_string()),
            api_key: Some("new-key".to_string()),
            api_type: Some("openai".to_string()),
            base_url: Some("https://api.new.com".to_string()),
            icon: Some("new-icon".to_string()),
            models: Some(vec![]),
            enabled: Some(false),
            description: Some("Updated description".to_string()),
        };

        let result = service.validate_update_operation(&regular_provider, &update_request);
        assert!(result.is_ok());
    }

    #[test]
    fn test_get_protection_rules() {
        let service = BuiltinProtectionService::new();
        let rules = service.get_protection_rules();

        assert_eq!(rules.len(), 4);
        assert!(rules.contains(&"内置模型提供商不能被删除"));
        assert!(rules.contains(&"内置模型提供商的名称不能被修改"));
        assert!(rules.contains(&"内置模型提供商的builtin字段不能被修改"));
        assert!(rules.contains(&"内置模型提供商的created字段不能被修改"));
    }

    #[test]
    fn test_validate_update_builtin_provider_other_fields_allowed() {
        let service = BuiltinProtectionService::new();
        let builtin_provider = create_builtin_provider();

        // 测试更新其他字段（非保护字段）应该被允许
        let update_request = UpdateModelProviderRequest {
            name: None, // 不修改名称
            api_key: Some("new-api-key".to_string()),
            api_type: Some("azure".to_string()),
            base_url: Some("https://api.azure.com".to_string()),
            icon: Some("azure-icon".to_string()),
            models: Some(vec![]),
            enabled: Some(false),
            description: Some("Updated Azure provider".to_string()),
        };

        let result = service.validate_update_operation(&builtin_provider, &update_request);
        assert!(result.is_ok());
    }

    #[test]
    fn test_validate_update_builtin_provider_empty_request_allowed() {
        let service = BuiltinProtectionService::new();
        let builtin_provider = create_builtin_provider();

        // 测试空的更新请求应该被允许
        let update_request = UpdateModelProviderRequest {
            name: None,
            api_key: None,
            api_type: None,
            base_url: None,
            icon: None,
            models: None,
            enabled: None,
            description: None,
        };

        let result = service.validate_update_operation(&builtin_provider, &update_request);
        assert!(result.is_ok());
    }
}
