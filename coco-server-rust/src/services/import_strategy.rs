use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use tracing::{debug, info, warn};

use crate::{error::result::Result, models::model_provider::ModelProvider};

/// 导入策略枚举
/// 定义不同的内置提供商导入策略
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum ImportStrategy {
    /// 跳过已存在的提供商，只导入新的
    SkipExisting,
    /// 更新已存在的提供商，但保留用户修改的敏感字段
    UpdateExisting,
    /// 强制覆盖所有字段（谨慎使用）
    ForceOverwrite,
}

/// 导入操作类型
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum ImportAction {
    /// 新建提供商
    Created,
    /// 更新提供商
    Updated,
    /// 跳过提供商
    Skipped,
    /// 导入失败
    Failed,
}

/// 单个提供商的导入详情
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ImportDetail {
    /// 提供商ID
    pub provider_id: String,
    /// 提供商名称
    pub provider_name: String,
    /// 执行的操作
    pub action: ImportAction,
    /// 是否成功
    pub success: bool,
    /// 错误信息（如果失败）
    pub error: Option<String>,
    /// 操作耗时（毫秒）
    pub duration_ms: u64,
    /// 操作时间戳
    pub timestamp: DateTime<Utc>,
}

/// 导入结果汇总
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ImportResult {
    /// 成功导入的提供商数量
    pub imported_count: usize,
    /// 跳过的提供商数量
    pub skipped_count: usize,
    /// 更新的提供商数量
    pub updated_count: usize,
    /// 失败的提供商数量
    pub failed_count: usize,
    /// 总处理数量
    pub total_count: usize,
    /// 导入详情列表
    pub details: Vec<ImportDetail>,
    /// 总耗时（毫秒）
    pub total_duration_ms: u64,
    /// 导入开始时间
    pub start_time: DateTime<Utc>,
    /// 导入结束时间
    pub end_time: DateTime<Utc>,
    /// 使用的导入策略
    pub strategy: ImportStrategy,
}

impl ImportResult {
    /// 创建新的导入结果
    pub fn new(strategy: ImportStrategy) -> Self {
        let now = Utc::now();
        Self {
            imported_count: 0,
            skipped_count: 0,
            updated_count: 0,
            failed_count: 0,
            total_count: 0,
            details: Vec::new(),
            total_duration_ms: 0,
            start_time: now,
            end_time: now,
            strategy,
        }
    }

    /// 添加导入详情
    pub fn add_detail(&mut self, detail: ImportDetail) {
        match detail.action {
            ImportAction::Created => self.imported_count += 1,
            ImportAction::Updated => self.updated_count += 1,
            ImportAction::Skipped => self.skipped_count += 1,
            ImportAction::Failed => self.failed_count += 1,
        }
        self.total_count += 1;
        self.details.push(detail);
    }

    /// 完成导入，计算总耗时
    pub fn finish(&mut self) {
        self.end_time = Utc::now();
        self.total_duration_ms = (self.end_time - self.start_time).num_milliseconds() as u64;
    }

    /// 检查是否有失败的导入
    pub fn has_failures(&self) -> bool {
        self.failed_count > 0
    }

    /// 获取成功率
    pub fn success_rate(&self) -> f64 {
        if self.total_count == 0 {
            return 1.0;
        }
        (self.total_count - self.failed_count) as f64 / self.total_count as f64
    }
}

/// 导入策略处理器
/// 负责根据不同策略处理提供商的导入逻辑
#[derive(Debug, Clone)]
pub struct ImportStrategyProcessor {
    strategy: ImportStrategy,
}

impl ImportStrategyProcessor {
    /// 创建新的策略处理器
    pub fn new(strategy: ImportStrategy) -> Self {
        Self { strategy }
    }

    /// 处理提供商导入
    ///
    /// # 参数
    /// - `new_provider`: 要导入的新提供商配置
    /// - `existing_provider`: 已存在的提供商（如果有）
    ///
    /// # 返回
    /// - `Ok(Some(provider))`: 需要保存的提供商数据
    /// - `Ok(None)`: 跳过此提供商
    /// - `Err(error)`: 处理失败
    pub fn process_provider(
        &self,
        new_provider: &ModelProvider,
        existing_provider: Option<&ModelProvider>,
    ) -> Result<Option<ModelProvider>> {
        match (&self.strategy, existing_provider) {
            // 策略：跳过已存在的提供商
            (ImportStrategy::SkipExisting, Some(_)) => {
                debug!("跳过已存在的提供商: {}", new_provider.name);
                Ok(None)
            }

            // 策略：更新已存在的提供商，保留敏感字段
            (ImportStrategy::UpdateExisting, Some(existing)) => {
                debug!("更新已存在的提供商: {}", new_provider.name);
                let updated = self.merge_providers(new_provider, existing)?;
                Ok(Some(updated))
            }

            // 策略：强制覆盖
            (ImportStrategy::ForceOverwrite, Some(_)) => {
                debug!("强制覆盖提供商: {}", new_provider.name);
                let mut provider = new_provider.clone();
                provider.updated = Utc::now();
                provider.builtin = true; // 确保内置标记
                Ok(Some(provider))
            }

            // 不存在的提供商，直接创建
            (_, None) => {
                debug!("创建新的提供商: {}", new_provider.name);
                let mut provider = new_provider.clone();
                let now = Utc::now();
                provider.created = now;
                provider.updated = now;
                provider.builtin = true; // 确保内置标记
                Ok(Some(provider))
            }
        }
    }

    /// 合并提供商配置，保留用户修改的敏感字段
    fn merge_providers(
        &self,
        new_provider: &ModelProvider,
        existing_provider: &ModelProvider,
    ) -> Result<ModelProvider> {
        let mut merged = new_provider.clone();

        // 保留原有的ID和时间戳
        merged.id = existing_provider.id.clone();
        merged.created = existing_provider.created;
        merged.updated = Utc::now();

        // 保留用户可能修改的敏感字段
        merged.api_key = existing_provider.api_key.clone();
        merged.enabled = existing_provider.enabled;

        // 如果用户自定义了base_url，保留用户的设置
        if !existing_provider.base_url.is_empty()
            && existing_provider.base_url != new_provider.base_url
        {
            warn!(
                "保留用户自定义的base_url: {} -> {}",
                new_provider.base_url, existing_provider.base_url
            );
            merged.base_url = existing_provider.base_url.clone();
        }

        // 确保内置标记
        merged.builtin = true;

        info!(
            "合并提供商配置完成: {} (保留api_key和enabled状态)",
            merged.name
        );

        Ok(merged)
    }

    /// 获取当前策略
    pub fn strategy(&self) -> &ImportStrategy {
        &self.strategy
    }
}

impl Default for ImportStrategy {
    fn default() -> Self {
        ImportStrategy::UpdateExisting
    }
}

impl std::fmt::Display for ImportStrategy {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            ImportStrategy::SkipExisting => write!(f, "跳过已存在"),
            ImportStrategy::UpdateExisting => write!(f, "更新已存在"),
            ImportStrategy::ForceOverwrite => write!(f, "强制覆盖"),
        }
    }
}

impl std::fmt::Display for ImportAction {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            ImportAction::Created => write!(f, "新建"),
            ImportAction::Updated => write!(f, "更新"),
            ImportAction::Skipped => write!(f, "跳过"),
            ImportAction::Failed => write!(f, "失败"),
        }
    }
}

#[cfg(test)]
mod tests {
    use chrono::Utc;
    use uuid::Uuid;

    use super::*;

    fn create_test_provider(name: &str, api_key: &str, enabled: bool) -> ModelProvider {
        use surrealdb::sql::{Id, Thing};

        ModelProvider {
            id: Some(Thing::from((
                "model_provider",
                Id::String(Uuid::new_v4().to_string()),
            ))),
            created: Utc::now(),
            updated: Utc::now(),
            name: name.to_string(),
            api_key: api_key.to_string(),
            api_type: "openai".to_string(),
            base_url: "https://api.example.com".to_string(),
            icon: "test_icon".to_string(),
            models: vec![],
            enabled,
            builtin: true,
            description: "测试提供商".to_string(),
        }
    }

    #[test]
    fn test_skip_existing_strategy() {
        let processor = ImportStrategyProcessor::new(ImportStrategy::SkipExisting);
        let new_provider = create_test_provider("test", "", false);
        let existing_provider = create_test_provider("test", "secret", true);

        let result = processor.process_provider(&new_provider, Some(&existing_provider));
        assert!(result.is_ok());
        assert!(result.unwrap().is_none());
    }

    #[test]
    fn test_update_existing_strategy() {
        let processor = ImportStrategyProcessor::new(ImportStrategy::UpdateExisting);
        let new_provider = create_test_provider("test", "", false);
        let existing_provider = create_test_provider("test", "secret", true);

        let result = processor.process_provider(&new_provider, Some(&existing_provider));
        assert!(result.is_ok());

        let merged = result.unwrap().unwrap();
        assert_eq!(merged.api_key, "secret"); // 保留原有api_key
        assert_eq!(merged.enabled, true); // 保留原有enabled状态
        assert_eq!(merged.id, existing_provider.id); // 保留原有ID
    }

    #[test]
    fn test_force_overwrite_strategy() {
        let processor = ImportStrategyProcessor::new(ImportStrategy::ForceOverwrite);
        let new_provider = create_test_provider("test", "new_key", false);
        let existing_provider = create_test_provider("test", "old_key", true);

        let result = processor.process_provider(&new_provider, Some(&existing_provider));
        assert!(result.is_ok());

        let overwritten = result.unwrap().unwrap();
        assert_eq!(overwritten.api_key, "new_key"); // 使用新的api_key
        assert_eq!(overwritten.enabled, false); // 使用新的enabled状态
    }

    #[test]
    fn test_create_new_provider() {
        let processor = ImportStrategyProcessor::new(ImportStrategy::UpdateExisting);
        let new_provider = create_test_provider("test", "key", true);

        let result = processor.process_provider(&new_provider, None);
        assert!(result.is_ok());

        let created = result.unwrap().unwrap();
        assert_eq!(created.name, "test");
        assert_eq!(created.builtin, true);
    }

    #[test]
    fn test_import_result() {
        let mut result = ImportResult::new(ImportStrategy::UpdateExisting);

        let detail1 = ImportDetail {
            provider_id: "1".to_string(),
            provider_name: "test1".to_string(),
            action: ImportAction::Created,
            success: true,
            error: None,
            duration_ms: 100,
            timestamp: Utc::now(),
        };

        let detail2 = ImportDetail {
            provider_id: "2".to_string(),
            provider_name: "test2".to_string(),
            action: ImportAction::Failed,
            success: false,
            error: Some("测试错误".to_string()),
            duration_ms: 50,
            timestamp: Utc::now(),
        };

        result.add_detail(detail1);
        result.add_detail(detail2);
        result.finish();

        assert_eq!(result.imported_count, 1);
        assert_eq!(result.failed_count, 1);
        assert_eq!(result.total_count, 2);
        assert!(result.has_failures());
        assert_eq!(result.success_rate(), 0.5);
    }
}
