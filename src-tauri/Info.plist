<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
  <key>NSCameraUsageDescription</key>
  <string>Request camera access for WebRTC</string>
  <key>NSMicrophoneUsageDescription</key>
  <string>Request microphone access for WebRTC</string>

	<key>CFBundleIdentifier</key>
	<string>rs.coco.app</string>
	<key>CFBundleExecutable</key>
    <string>coco</string>
	<key>NSPrefPaneIconLabel</key>
	<string>coco-ai</string>

	<key>LSUIElement</key>
    <true/>

      <key>ITSAppUsesNonExemptEncryption</key>
      <false/>

  <key>CFBundleURLTypes</key>
  <array>
    <dict>
      <key>CFBundleURLName</key>
      <string>rs.coco.app</string>
      <key>CFBundleURLSchemes</key>
      <array>
        <string>coco</string>
      </array>
    </dict>
  </array>
  
  <key>NSMicrophoneUsageDescription</key>
  <string>Coco AI needs access to your microphone for voice input and audio recording features.</string>
  <key>NSCameraUsageDescription</key>
  <string>Coco AI requires camera access for scanning documents and capturing images.</string>
  <key>NSSpeechRecognitionUsageDescription</key>
  <string>Coco AI uses speech recognition to convert your voice into text for a hands-free experience.</string>
</dict>
</plist>