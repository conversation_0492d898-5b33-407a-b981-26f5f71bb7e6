import { useThemeStore } from "@/stores/themeStore";
import { FC, useMemo } from "react";

interface NoDataImageProps {
  size?: number;
}

const NoDataImage: FC<NoDataImageProps> = (props) => {
  const { size = 64 } = props;
  const isDark = useThemeStore((state) => state.isDark);

  const color = useMemo(() => {
    return isDark ? "#666" : "#bbb";
  }, [isDark]);

  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 64 64"
      version="1.1"
      xmlns="http://www.w3.org/2000/svg"
      xmlnsXlink="http://www.w3.org/1999/xlink"
    >
      <title>编组</title>
      <defs>
        <rect id="path-1" x="0" y="0" width="64" height="64"></rect>
      </defs>
      <g
        id="聊天记录"
        stroke="none"
        strokeWidth="1"
        fill="none"
        fillRule="evenodd"
      >
        <g id="画板" transform="translate(-1164, -252)">
          <g id="编组" transform="translate(1164, 252)">
            <mask id="mask-2" fill="white">
              <use xlinkHref="#path-1"></use>
            </mask>
            <g id="矩形"></g>
            <path
              d="M42.6791711,10.4781665 C43.2314559,10.4781665 43.6791711,10.9258817 43.6791711,11.4781665 C43.6791711,12.0304512 43.2314559,12.4781665 42.6791711,12.4781665 L22.9821691,12.4781665 C20.8942749,12.4781665 19.1821691,14.272673 19.1821691,16.5091825 L19.1821691,52.5647975 C19.1821691,54.801307 20.8942749,56.5958135 22.9821691,56.5958135 L52.5821691,56.5958135 C54.6700634,56.5958135 56.3821691,54.801307 56.3821691,52.5647975 L56.3821691,35.1361358 C56.3821691,34.5838511 56.8298844,34.1361358 57.3821691,34.1361358 C57.9344539,34.1361358 58.3821691,34.5838511 58.3821691,35.1361358 L58.3821691,52.5647975 C58.3821691,55.8853949 55.7962085,58.5958135 52.5821691,58.5958135 L22.9821691,58.5958135 C19.7681298,58.5958135 17.1821691,55.8853949 17.1821691,52.5647975 L17.1821691,16.5091825 C17.1821691,13.1885852 19.7681298,10.4781665 22.9821691,10.4781665 L42.6791711,10.4781665 Z"
              id="路径"
              fill={color}
              fillRule="nonzero"
              mask="url(#mask-2)"
              transform="translate(37.7822, 34.537) scale(-1, -1) translate(-37.7822, -34.537)"
            ></path>
            <path
              d="M13.0320895,0.82004591 C13.5510674,0.631153401 14.1249097,0.898740484 14.3138023,1.41771839 L15.8235355,5.56567639 C16.012428,6.0846543 15.7448409,6.65849665 15.225863,6.84738916 C14.7068851,7.03628167 14.1330428,6.76869458 13.9441503,6.24971668 L12.434417,2.10175867 C12.2455245,1.58278077 12.5131116,1.00893842 13.0320895,0.82004591 Z"
              id="路径-4"
              fill={color}
              fillRule="nonzero"
              mask="url(#mask-2)"
            ></path>
            <path
              d="M21.302281,2.6499257 C21.6572828,2.22685104 22.2880384,2.17166708 22.7111131,2.52666887 C23.1341877,2.88167066 23.1893717,3.51242626 22.8343699,3.93550092 L19.9969995,7.31694727 C19.6419977,7.74002193 19.0112421,7.7952059 18.5881675,7.4402041 C18.1650928,7.08520231 18.1099088,6.45444672 18.4649106,6.03137205 L21.302281,2.6499257 Z"
              id="路径-4"
              fill={color}
              fillRule="nonzero"
              mask="url(#mask-2)"
            ></path>
            <path
              d="M12.7902454,12.7943281 C13.1452472,12.3712534 13.7760028,12.3160695 14.1990774,12.6710713 C14.6221521,13.0260731 14.677336,13.6568287 14.3223342,14.0799033 L11.4849639,17.4613497 C11.1299621,17.8844243 10.4992065,17.9396083 10.0761318,17.5846065 C9.65305715,17.2296047 9.59787319,16.5988491 9.95287498,16.1757745 L12.7902454,12.7943281 Z"
              id="路径-4"
              fill={color}
              fillRule="nonzero"
              mask="url(#mask-2)"
            ></path>
            <path
              d="M7.75703715,7.52422963 L7.8730548,7.53785515 L12.2201584,8.30436681 C12.7640527,8.40027005 13.1272212,8.91892844 13.031318,9.46282274 C12.9354148,10.006717 12.4167564,10.3698856 11.8728621,10.2739823 L7.52575844,9.50747066 C6.98186414,9.41156742 6.61869562,8.89290903 6.71459887,8.34901473 C6.81050211,7.80512042 7.32916049,7.44195191 7.8730548,7.53785515 L7.75703715,7.52422963 Z"
              id="路径-4"
              fill={color}
              fillRule="nonzero"
              mask="url(#mask-2)"
            ></path>
            <g id="编组-7" mask="url(#mask-2)" fill={color} fillRule="nonzero">
              <g transform="translate(21.7188, 46.9969) rotate(18) translate(-21.7188, -46.9969)translate(1.0617, 26.0967)">
                <path
                  d="M29.4293481,8.47638559 C29.6966411,8.47287826 29.9542113,8.57653439 30.1445628,8.76421534 L34.821948,13.37598 C35.2152213,13.7637359 35.219694,14.3968851 34.8319381,14.7901583 C34.4441822,15.1834316 33.811033,15.1879043 33.4177598,14.8001484 L29.0372657,10.4802995 L10.8832657,10.7192995 L7.15822377,14.4638747 C6.79868609,14.8253024 6.23152968,14.8545189 5.83844017,14.5505235 L5.74401507,14.4675822 C5.35246833,14.078083 5.35080843,13.4449202 5.74030758,13.0533735 L9.75247731,9.02011185 C9.93695686,8.8346625 10.1867572,8.72888061 10.4483149,8.72544853 L29.4293481,8.47638559 Z"
                  id="路径"
                ></path>
                <path
                  d="M21.9935139,5.99478408 C22.1098028,5.454881 22.6417515,5.11147388 23.1816546,5.22776279 C30.6189355,6.82966762 36.013841,13.4326353 36.013841,21.1370431 C36.013841,30.1250238 28.7276429,37.4112219 19.7396622,37.4112219 C13.7007194,37.4112219 8.24610098,34.0926516 5.42139758,28.8785984 C5.15832308,28.3929954 5.33871807,27.7860724 5.82432105,27.5229979 C6.30992403,27.2599235 6.91684704,27.4403184 7.17992153,27.9259214 C9.65840757,32.5009002 14.442002,35.4112219 19.7396622,35.4112219 C27.6230734,35.4112219 34.013841,29.0204543 34.013841,21.1370431 C34.013841,14.3797233 29.2812816,8.58741864 22.7605351,7.18292479 C22.2206321,7.06663587 21.8772249,6.53468716 21.9935139,5.99478408 Z"
                  id="路径"
                  transform="translate(20.6572, 21.3082) rotate(54) translate(-20.6572, -21.3082)"
                ></path>
                <path
                  d="M24.8408727,10.2022416 C25.1806942,9.48260473 26.1841494,9.42898516 26.5987243,10.1083109 C28.1725233,12.6871503 30.5237061,13.5670326 33.8949611,12.7889293 L34.3447467,14.7376963 C30.8069102,15.5542475 27.9486137,14.8138981 25.9168841,12.5414695 L25.7964185,12.4022407 L25.7898204,12.4127995 C25.6463328,12.5945684 25.4927076,12.7728669 25.3285985,12.9461789 L25.0745286,13.2022175 C23.8489955,14.3805113 22.3059749,15.0880642 20.4542955,15.0880642 C18.3100422,15.0880642 16.4792823,14.2999241 15.006077,12.7631502 L14.8394185,12.5842407 L14.8279264,12.601442 C13.2192845,14.7385979 10.485016,15.3662742 6.82944359,14.516796 L6.52268429,14.4425875 L7.01015268,12.5029033 C10.675886,13.4241508 12.8140347,12.7029257 13.7976491,10.3982465 C14.1109032,9.66426959 15.1154408,9.57488536 15.5533666,10.2420219 C16.8166654,12.1665323 18.4200295,13.0880642 20.4542955,13.0880642 C21.7518359,13.0880642 22.8143045,12.6008686 23.6883743,11.7604906 C24.2529251,11.217701 24.6339476,10.6404451 24.8408727,10.2022416 Z"
                  id="路径-16"
                ></path>
                <path
                  d="M12.8244173,23.0152776 C13.1494608,21.9675928 14.6803967,22.1318925 14.7757241,23.2246913 C15.2805735,29.0121052 16.9225833,32.5211991 19.5882786,33.8687679 C22.99019,35.5885105 25.2983354,32.9466769 25.9867311,26.7356659 C26.1186972,25.5450097 27.8517173,25.5511021 27.9753088,26.7426567 C28.3760471,30.6062039 28.258208,33.1868933 27.5394161,34.6115345 C27.2906367,35.1046138 26.6892413,35.302658 26.196162,35.0538786 C25.8791824,34.8939489 25.6841306,34.5882954 25.6513323,34.2593739 L25.6474421,34.1795936 L25.5912925,34.2588436 C23.9966984,36.4166078 21.7167087,37.0904273 18.9087496,35.7626542 L18.6859746,35.6536621 C16.4357028,34.5160993 14.8302856,32.3368304 13.837234,29.149295 L13.7894421,28.9905936 L13.7871985,29.0725484 C13.7433455,31.0241936 14.0745615,32.6398281 14.6834347,33.7216018 L14.7874678,33.8969203 C15.0804319,34.3650982 14.938393,34.9821256 14.4702152,35.2750897 C14.0020373,35.5680537 13.3850099,35.4260149 13.0920458,34.957837 C11.5454425,32.4862525 11.2726605,28.0169212 12.8244173,23.0152776 Z"
                  id="路径-17"
                ></path>
                <path
                  d="M34.9080784,-0.312138826 C35.4592592,-0.347040678 35.9343731,0.0714861262 35.9692749,0.622666955 C36.0041768,1.17384778 35.58565,1.64896167 35.0344692,1.68386352 L26.467279,2.22586235 L24.1964917,8.55308868 C24.0232482,9.03577628 23.5171178,9.30318989 23.0295024,9.18955089 L22.9174644,9.1564868 C22.397647,8.96991696 22.1274965,8.39727695 22.3140663,7.87745954 L24.8068618,0.932078859 C24.9416364,0.556572435 25.2867164,0.297104704 25.6848791,0.271892256 L34.9080784,-0.312138826 Z"
                  id="路径-20"
                  transform="translate(29.1132, 4.4507) rotate(-7) translate(-29.1132, -4.4507)"
                ></path>
              </g>
            </g>
            <path
              d="M49.865098,19.7058824 C50.4173828,19.7058824 50.865098,20.1535976 50.865098,20.7058824 C50.865098,21.2581671 50.4173828,21.7058824 49.865098,21.7058824 L26.0259167,21.7058824 C25.4736319,21.7058824 25.0259167,21.2581671 25.0259167,20.7058824 C25.0259167,20.1535976 25.4736319,19.7058824 26.0259167,19.7058824 L49.865098,19.7058824 Z"
              id="路径-7"
              fill={color}
              fillRule="nonzero"
              mask="url(#mask-2)"
            ></path>
            <path
              d="M49.865098,39.4705882 C50.4173828,39.4705882 50.865098,39.9183035 50.865098,40.4705882 C50.865098,41.022873 50.4173828,41.4705882 49.865098,41.4705882 L43.2941176,41.4705882 C42.7418329,41.4705882 42.2941176,41.022873 42.2941176,40.4705882 C42.2941176,39.9183035 42.7418329,39.4705882 43.2941176,39.4705882 L49.865098,39.4705882 Z"
              id="路径-7备份"
              fill={color}
              fillRule="nonzero"
              mask="url(#mask-2)"
            ></path>
            <path
              d="M49.865098,47 C50.4173828,47 50.865098,47.4477153 50.865098,48 C50.865098,48.5522847 50.4173828,49 49.865098,49 L43.2941176,49 C42.7418329,49 42.2941176,48.5522847 42.2941176,48 C42.2941176,47.4477153 42.7418329,47 43.2941176,47 L49.865098,47 Z"
              id="路径-7备份-2"
              fill={color}
              fillRule="nonzero"
              mask="url(#mask-2)"
            ></path>
          </g>
        </g>
      </g>
    </svg>
  );
};

export default NoDataImage;
