import { AssistantFetcher } from "@/components/Assistant/AssistantFetcher";
import SettingsSelectPro from "@/components/Settings/SettingsSelectPro";
import { useAppStore } from "@/stores/appStore";
import platformAdapter from "@/utils/platformAdapter";
import { useAsyncEffect, useMount } from "ahooks";
import { FC, useMemo, useState } from "react";
import { ExtensionId } from "../../..";
import { useTranslation } from "react-i18next";
import { isArray } from "lodash-es";

interface SharedAiProps {
  id: ExtensionId;
  server?: any;
  setServer: (server: any) => void;
  assistant?: any;
  setAssistant: (assistant: any) => void;
}

const SharedAi: FC<SharedAiProps> = (props) => {
  const { id, server, setServer, assistant, setAssistant } = props;

  const [serverList, setServerList] = useState<any[]>([server]);
  const [assistantList, setAssistantList] = useState<any[]>([assistant]);
  const addError = useAppStore((state) => state.addError);
  const { fetchAssistant } = AssistantFetcher({});
  const { t } = useTranslation();
  const [assistantSearchValue, setAssistantSearchValue] = useState("");

  useMount(async () => {
    try {
      const data = await platformAdapter.invokeBackend<any[]>(
        "list_coco_servers"
      );

      if (isArray(data)) {
        const enabledServers = data.filter(
          (s) => s.enabled && s.available && (s.public || s.profile)
        );

        setServerList(enabledServers);

        if (server) {
          const matchServer = enabledServers.find((item) => {
            return item.id === server.id;
          });

          if (matchServer) {
            return setServer(matchServer);
          }
        }

        setServer(enabledServers[0]);
      }
    } catch (error) {
      addError(String(error));
    }
  });

  useAsyncEffect(async () => {
    try {
      if (!server) return;

      const data = await fetchAssistant({
        current: 1,
        pageSize: 1000,
        serverId: server.id,
        query: assistantSearchValue,
      });

      const list = data.list.map((item: any) => item._source);

      setAssistantList(list);

      if (assistant) {
        const matched = list.find((item: any) => {
          return item.id === assistant.id;
        });

        if (matched) {
          return setAssistant(matched);
        }
      }

      setAssistant(list[0]);
    } catch (error) {
      addError(String(error));
    }
  }, [server, assistantSearchValue]);

  const selectList = useMemo(() => {
    return [
      {
        label: t(
          "settings.extensions.shardAi.details.linkedAssistant.label.cocoServer"
        ),
        value: server?.id,
        icon: server?.provider?.icon,
        data: serverList,
        searchable: false,
        onChange: (value: string) => {
          const matched = serverList.find((item) => item.id === value);

          setServer(matched);
        },
      },
      {
        label: t(
          "settings.extensions.shardAi.details.linkedAssistant.label.aiAssistant"
        ),
        value: assistant?.id,
        icon: assistant?.icon,
        data: assistantList,
        searchable: true,
        onChange: (value: string) => {
          const matched = assistantList.find((item) => item.id === value);

          setAssistant(matched);
        },
        onSearch: (value: string) => {
          setAssistantSearchValue(value);
        },
      },
    ];
  }, [serverList, assistantList, server, assistant]);

  const renderDescription = () => {
    if (id === "QuickAIAccess") {
      return t("settings.extensions.quickAiAccess.description");
    }

    if (id === "AIOverview") {
      return t("settings.extensions.aiOverview.description");
    }
  };

  return (
    <>
      <div className="text-[#999]">{renderDescription()}</div>

      <div className="mt-6 text-[#333] dark:text-white/90">
        {t("settings.extensions.shardAi.details.linkedAssistant.title")}
      </div>

      {selectList.map((item) => {
        const { label, value, data, searchable, onChange, onSearch } = item;

        return (
          <div key={label} className="mt-4">
            <div className="mb-2 text-[#666] dark:text-white/70">{label}</div>

            <SettingsSelectPro
              value={value}
              options={data}
              searchable={searchable}
              onChange={onChange}
              onSearch={onSearch}
            />
          </div>
        );
      })}
    </>
  );
};

export default SharedAi;
