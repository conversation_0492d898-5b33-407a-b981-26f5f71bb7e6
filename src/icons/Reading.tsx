import SVGWrap from "./SVGWrap";

export default function Reading(props: I.SVG) {
  return (
    <SVGWrap {...props} viewBox="0 0 16 16">
      <g
        id="deading"
        stroke="none"
        strokeWidth="1"
        fill="none"
        fillRule="evenodd"
      >
        <circle
          id="椭圆形"
          stroke="currentColor"
          strokeWidth="1.25"
          cx="8"
          cy="3"
          r="1.375"
        ></circle>
        <circle
          id="椭圆形备份-2"
          stroke="currentColor"
          strokeWidth="1.25"
          cx="3"
          cy="5"
          r="1.375"
        ></circle>
        <circle
          id="椭圆形备份-4"
          stroke="currentColor"
          strokeWidth="1.25"
          cx="13"
          cy="5"
          r="1.375"
        ></circle>
        <circle
          id="椭圆形备份"
          stroke="currentColor"
          strokeWidth="1.25"
          cx="8"
          cy="13"
          r="1.375"
        ></circle>
        <circle
          id="椭圆形备份-3"
          stroke="currentColor"
          strokeWidth="1.25"
          cx="3"
          cy="11"
          r="1.375"
        ></circle>
        <circle
          id="椭圆形备份-5"
          stroke="currentColor"
          strokeWidth="1.25"
          cx="13"
          cy="11"
          r="1.375"
        ></circle>
        <path
          d="M8.0070039,4.03345364 L8.0070039,8.50590493 L4.1923477,10.2855921"
          id="路径-13"
          stroke="currentColor"
          strokeWidth="1.25"
        ></path>
        <line
          x1="11.7924093"
          y1="8.47550067"
          x2="8"
          y2="10.2754456"
          id="路径-13备份"
          stroke="currentColor"
          strokeWidth="1.25"
          transform="translate(9.8962, 9.3755) scale(-1, 1) translate(-9.8962, -9.3755)"
        ></line>
        <path
          d="M4.17568738,4.53038288 L6.65384701,3.54050563 M9.35480875,3.53987819 L11.7283558,4.49062879"
          id="形状"
          stroke="currentColor"
          strokeWidth="1.25"
        ></path>
        <line
          x1="3"
          y1="6.06946104"
          x2="3"
          y2="9.97988046"
          id="路径-14"
          stroke="currentColor"
          strokeWidth="1.25"
        ></line>
        <line
          x1="13"
          y1="6.06946104"
          x2="13"
          y2="9.97988046"
          id="路径-14备份"
          stroke="currentColor"
          strokeWidth="1.25"
        ></line>
      </g>
    </SVGWrap>
  );
}
