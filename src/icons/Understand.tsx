import SVGWrap from "./SVGWrap";

export default function Understand(props: I.SVG) {
  return (
    <SVGWrap {...props} viewBox="0 0 16 16">
      <g
        id="Understand"
        stroke="none"
        strokeWidth="1"
        fill="none"
        fillRule="evenodd"
        strokeLinecap="round"
        strokeLinejoin="round"
      >
        <g
          id="编组"
          transform="translate(0.5, 0.5)"
          stroke="currentColor"
          strokeWidth="1.25"
        >
          <path
            d="M7.44444444,3 C9.89904333,3 11.8888889,4.95366655 11.8888889,7.36363636 C11.8888889,9.06711551 10.8946979,10.5426108 9.44492275,11.2613085 L9.44444444,12.2727273 C9.44444444,13.3772968 8.54901394,14.2727273 7.44444444,14.2727273 C6.33987494,14.2727273 5.44444444,13.3772968 5.44444444,12.2727273 L5.44396614,11.2613085 C3.99419095,10.5426108 3,9.06711551 3,7.36363636 C3,4.95366655 4.98984556,3 7.44444444,3 Z"
            id="形状结合"
          ></path>
          <line
            x1="5.5"
            y1="11.2156017"
            x2="9.5"
            y2="11.2156017"
            id="路径-11"
          ></line>
          <line
            x1="7.44444444"
            y1="10.6363636"
            x2="7.44444444"
            y2="8.45454545"
            id="路径-12"
          ></line>
          <line
            x1="7.44444444"
            y1="0.818181818"
            x2="7.44444444"
            y2="0.272727273"
            id="路径-12备份"
          ></line>
          <line
            x1="12.352383"
            y1="2.81770629"
            x2="12.3574335"
            y2="2.26720124"
            id="路径-12备份"
            transform="translate(12.3549, 2.5425) rotate(45) translate(-12.3549, -2.5425)"
          ></line>
          <line
            x1="14.3888889"
            y1="7.64141414"
            x2="14.3888889"
            y2="7.08585859"
            id="路径-12备份"
            transform="translate(14.3889, 7.3636) rotate(90) translate(-14.3889, -7.3636)"
          ></line>
          <line
            x1="12.3574335"
            y1="12.4600715"
            x2="12.352383"
            y2="11.9095664"
            id="路径-12备份"
            transform="translate(12.3549, 12.1848) rotate(135) translate(-12.3549, -12.1848)"
          ></line>
          <line
            x1="2.53145543"
            y1="12.4600715"
            x2="2.53650594"
            y2="11.9095664"
            id="路径-12备份"
            transform="translate(2.534, 12.1848) rotate(225) translate(-2.534, -12.1848)"
          ></line>
          <line
            x1="0.5"
            y1="7.64141414"
            x2="0.5"
            y2="7.08585859"
            id="路径-12备份"
            transform="translate(0.5, 7.3636) rotate(270) translate(-0.5, -7.3636)"
          ></line>
          <line
            x1="2.53650594"
            y1="2.81770629"
            x2="2.53145543"
            y2="2.26720124"
            id="路径-12备份"
            transform="translate(2.534, 2.5425) rotate(315) translate(-2.534, -2.5425)"
          ></line>
          <polyline
            id="路径-15"
            transform="translate(7.4398, 6.7308) rotate(-45) translate(-7.4398, -6.7308)"
            points="6.33632897 5.60310185 6.3568375 7.83853192 8.5432888 7.85859111"
          ></polyline>
        </g>
      </g>
    </SVGWrap>
  );
}
