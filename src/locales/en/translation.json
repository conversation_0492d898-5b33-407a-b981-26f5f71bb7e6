{"settings": {"general": "General Settings", "startup": {"title": "Startup", "description": "Automatically start Coco when you login", "toggle": "Launch at login"}, "hotkey": {"title": "Coco Hotkey", "description": "Global shortcut to open Coco"}, "appearance": {"title": "Appearance", "description": "Choose your preferred theme", "light": "Light", "dark": "Dark", "auto": "Auto"}, "language": {"title": "Language", "description": "Choose your preferred language", "english": "English", "chinese": "简体中文"}, "tooltip": {"title": "<PERSON><PERSON><PERSON>", "description": "Tooltip display for shortcut keys", "toggle": "Tooltip display"}, "shortcut": {"pressKeys": "Press keys...", "save": "Save", "edit": "Edit"}, "about": {"logo": "Coco Logo", "slogan": "Search, Connect, Collaborate—All in one place", "website": "Visit Website", "github": "Visit GitHub", "version": "Version {{version}}", "copyright": "©{{year}} INFINI Labs, All Rights Reserved."}, "advanced": {"title": "Advanced Settings", "endpoint": {"title": "API Endpoint", "description": "Domain name for interface and websocket"}, "startup": {"title": "Startup Settings", "defaultStartupWindow": {"title": "Default Startup Window", "description": "Set whether Coco opens in Search or Chat mode.", "select": {"searchMode": "Search Mode", "chatMode": "Chat Mode"}}, "defaultContentForSearchWindow": {"title": "Default Content for Search Window", "description": "Set what shows by default in Search mode.", "select": {"systemDefault": "System Default"}}, "defaultContentForChatWindow": {"title": "Default Content for Chat Window", "description": "Set what shows by default in Chat mode.", "select": {"newChat": "Start new chat", "oldChat": "Continue previous chat"}}}, "shortcuts": {"title": "Keyboard Shortcuts", "modifierKey": {"title": "Modifier Key", "description": "Set the primary modifier key for custom shortcuts."}, "modeSwitch": {"title": "Mode Switch", "description": "Shortcut to toggle between Search and Chat modes."}, "returnToInput": {"title": "Return to Input", "description": "Shortcut to go back to the input area in both Search and Chat modes."}, "voiceInput": {"title": "Voice Input", "description": "Shortcut to activate voice input function in Search mode."}, "addImage": {"title": "Add Image", "description": "Shortcut to quickly add images in Search mode."}, "selectLlmModel": {"title": "Select LLM Model", "description": "Shortcut to select the desired LLM model in Chat mode."}, "addFile": {"title": "Add File", "description": "Shortcut to add files conveniently in Chat mode."}, "deepThinking": {"title": "Deep Thinking", "description": "Shortcut to activate Deep Thinking Mode in chat mode."}, "internetSearch": {"title": "Internet Search", "description": "Shortcut button to enable networking search in chat mode."}, "internetSearchScope": {"title": "Internet Search Scope", "description": "Shortcut to open the networking search scope menu in chat mode."}, "mcpSearch": {"title": "MCP Search", "description": "Shortcut button to enable MCP search in chat mode."}, "mcpSearchScope": {"title": "MCP <PERSON> Scope", "description": "Shortcut to open the MCP search scope menu in chat mode."}, "historicalRecords": {"title": "Conversation History", "description": "Shortcut to view past conversation history in chat mode."}, "aiAssistant": {"title": "AI Assistant", "description": "Shortcut to view AI assistant list in chat mode."}, "newSession": {"title": "New Conversation", "description": "Shortcut to start a new conversation in chat mode."}, "fixedWindow": {"title": "Fixed Window", "description": "Shortcut to keep the Coco window always on top of other windows."}, "serviceList": {"title": "Service Panel", "description": "Shortcut to open the service panel in chat mode."}, "external": {"title": "Detach Window", "description": "Shortcut to open the current conversation as an independent window in chat mode."}, "hints": {"isSystem": "The shortcut key \"{{0}}\" is reserved by the system. Please choose a different key.", "isUse": "The shortcut key \"{{0}}\" is already used by \"{{1}}\". Please choose a different key."}, "aiOverview": {"title": "AI Overview", "description": "Shortcut button to enable AI Overview in chat mode."}}, "connect": {"title": "Connection Settings", "connectionTimeout": {"title": "Connection Timeout", "description": "Retries the connection if no response is received within this time. Default: 120s."}, "queryTimeout": {"title": "Query Timeout", "description": "Terminates the query if no search results are returned within this time. Default: 500ms."}, "allowSelfSignature": {"title": "Allow Self-Signed Certificates", "description": "Allow connections to servers using self-signed certificates. Enable only if you trust the source."}}, "appearance": {"title": "Appearance Settings", "opacity": {"title": "Pinned Window Dimness Setting", "description": "Adjusts the opacity level of the Coco AI window when it’s pinned and not in focus. Set a value between 10% and 100%, where 100% means fully opaque (no dimming), and lower values increase transparency, allowing underlying content to show through."}}, "updateVersion": {"title": "Version & Updates", "snapshotUpdate": {"title": "Snapshot Updates", "description": "Get early access to new features. May be unstable."}}}, "tabs": {"general": "General", "extensions": "Extensions", "connect": "Connect", "advanced": "Advanced", "about": "About", "extensionsContent": "Extensions settings content", "advancedContent": "Advanced Settings content"}, "extensions": {"title": "Extensions", "list": {"name": "Name", "type": "Type", "alias": "<PERSON><PERSON>", "hotkey": "<PERSON><PERSON>", "enabled": "Enabled"}, "hints": {"addAlias": "<PERSON><PERSON>", "recordHotkey": "Record Hotkey"}, "application": {"title": "Applications", "details": {"searchScope": "<PERSON> Scope", "rebuildIndex": "Rebuild Index", "reindex": "Rebuild Index", "searchScopeDescription": "Directories added here will be searched for applications and preference panes.", "rebuildIndexDescription": "Rebuild the index to search the latest application list.", "name": "Name", "where": "Where", "type": "Type", "typeValue": "Application", "size": "Size", "created": "Created", "modified": "Modified", "lastOpened": "Last Opened"}, "hints": {"reindexSuccess": "Index rebuilt successfully. The latest application list is now updated.", "reindexFailed": "Failed to rebuild the index. Please try again later."}}, "calculator": {"title": "Calculator", "description": "A calculator you can quickly invoke in the search bar, supporting basic math operations."}, "shardAi": {"details": {"linkedAssistant": {"title": "Linked Assistant", "label": {"cocoServer": "Coco Server", "aiAssistant": "AI Assistant"}}}}, "quickAiAccess": {"description": "Quick AI access allows you to start a conversation immediately from the search box using the Tab key."}, "aiOverview": {"description": "AI Overview generates concise summaries based on your search results, helping you quickly grasp key information without reading every document.", "details": {"aiOverviewTrigger": {"title": "AI Overview Trigger", "description": "AI Overview will be triggered only when all of the following conditions are met.(search results are sourced from Coco Server's result statistics)", "label": {"minCharLen": "Minimum Input Length(characters)", "minDelay": "Delay After Typing Stops(seconds)", "minQuantity": "Minimum Number of Search Results"}}}}, "directoryScope": {"hints": {"pathDuplication": "Path \"{{0}}\" is already in search scope.", "pathIncluded": "Path \"{{0}}\" is already covered by another search directory."}, "button": {"addDirectories": "Add Directories"}}, "fileSearch": {"description": " Search for files and folders on your computer.", "label": {"searchBy": "Search By", "searchScope": "<PERSON> Scope", "excludeScope": "Exclude <PERSON><PERSON>", "searchFileTypes": "Search File Types", "name": "Name", "nameAndContents": "Name and Contents"}, "hints": {"typeExists": "File type already exists."}}}}, "search": {"textarea": {"placeholder": "Ask whatever you want ...", "ariaLabel": "Ask whatever you want"}, "document": {"details": "Details", "name": "Name", "source": "Source", "url": "URL", "thumbnail": "<PERSON><PERSON><PERSON><PERSON>", "id": "ID", "createdAt": "Created At", "category": "Category", "richCategories": "RichCategories", "subcategory": "Subcategory", "language": "Language", "tags": "Tags", "summary": "Summary", "updatedAt": "Updated At", "updatedBy": "Updated By", "createdBy": "Created By", "type": "Type", "size": "Size"}, "list": {"loading": "Loading...", "noResults": "No Results", "noDataAlt": "No data image", "failures": "Partial results returned due to service failures."}, "footer": {"logoAlt": "Coco Logo", "version": "{{version}}", "updateAvailable": "Update available", "select": "Select", "open": "Open", "powered": "Powered by Coco AI", "install": "Install", "details": "Details", "uninstall": "Uninstall"}, "input": {"searchPlaceholder": "Search whatever you want ...", "connectionError": "Unable to connect to the server", "reconnect": "Click here to reconnect.", "connecting": "Connecting", "deepThink": "Deep Think", "search": "Search", "MCP": "MCP", "uploadFile": "Upload File", "screenshot": "Screenshot", "screenshotType": {"screen": "Screen", "window": "Window"}, "searchPopover": {"title": "<PERSON> Scope", "allScope": "<PERSON> Scope"}, "uploadFileHints": {"tooltip": "Support screenshots, upload files, up to 50, single file up to 100 MB.", "maxSize": "The file size cannot exceed 100 MB."}}, "main": {"noDataAlt": "No data image", "noResults": "No Results", "askCoco": "Ask Coco AI"}, "header": {"found": "Found", "results": "results"}, "contextMenu": {"open": "Open", "copyLink": "Copy Link", "copyAnswer": "Copy Answer", "copyUppercaseAnswer": "Copy Answer (in Word)", "copyQuestionAndAnswer": "Copy Question and Answer", "title": {"calculator": "Calculator"}, "search": "Search Operation", "details": "Details", "install": "Install", "uninstall": "Uninstall", "configureExtension": "Configure Extension"}, "askCocoAi": {"title": "{{0}} {{1}}", "placeholder": "Ask More", "continueInChat": "Continue in chat", "copy": "Copy"}}, "assistant": {"popover": {"title": "AI Assistant", "search": "Search"}, "chat": {"openChat": "Open Chat Window", "newChat": "New Chat", "connectionError": "Unable to connect to the server", "reconnect": "Reconnect", "greetings": "Hi! I’m <PERSON><PERSON>, nice to meet you. I can help answer your questions by tapping into the internet and your data sources. How can I assist you today?", "timedout": "Request timed out. Please try again later.", "noServers": "No servers found", "addServer": "Add Server", "servers": "Servers", "aiAssistant": "AI Assistant", "error": "Request error. Please try again later.", "logo_alt": "<PERSON><PERSON>", "welcome": "Welcome to Coco AI", "connect_tip": "To start a conversation, please connect to the service and log in to your account.", "connect": "Connect"}, "input": {"stopMessage": "Stop message", "deepThink": "Deep Think", "deepThinkTooltip": "Enable deep thinking mode", "search": "Search", "searchTooltip": "Enable search mode"}, "message": {"logo": "Coco AI Logo", "aiName": "Coco AI", "thinkingButton": "View thinking process", "steps": {"query_intent": "Understand the query", "tools": "Call LLM Tools", "source_zero": "Searching for relevant documents", "fetch_source": "Retrieve {{count}} documents", "pick_source": "Intelligent pick {{count}} results", "pick_source_start": "Intelligently pre-selecting", "deep_read": "Deep reading", "think": "AI is thinking...", "thoughtTime": "Thought for a few seconds", "keywords": "Keywords", "questionType": "Query Type", "userIntent": "User Intent", "relatedQuestions": "Query", "suggestion": "Suggestion", "informationSeeking": "Information Seeking"}}, "sidebar": {"newChat": "New Chat", "newChatTooltip": "Create a new chat", "selectChat": "Select this chat", "untitledChat": "Untitled Chat"}, "source": {"fetch_source": "Found {{count}} results", "pick_source": "{{count}} results"}, "fileList": {"uploading": "Uploading...", "uploaded": "Uploaded"}, "sessionFile": {"title": "Files in the conversation", "description": "Only the selected files will participate in the current conversation"}}, "cloud": {"banner": "Banner Image", "accountInfo": "Account Information", "login": "<PERSON><PERSON>", "cancel": "Cancel", "copyUrl": "Copy URL", "eula": "EULA", "privacyPolicy": "Privacy Policy", "connect": {"back": "Back", "title": "Connecting to Your Coco-Server", "description": "Running your own private instance of coco-server ensures complete control over your data, keeping it secure and accessible only within your environment. Enjoy enhanced privacy, better performance, and seamless integration with your internal systems.", "serverAddress": "Server address", "serverPlaceholder": "For example: https://coco.infini.cloud/", "connecting": "Connecting...", "connect": "Connect", "closeError": "Close error message"}, "dataSource": {"title": "Data Source", "refresh": "Refresh data source list"}, "sidebar": {"selectServer": "Select Server", "serverLogo": "Server Logo", "serverOnline": "Server Online", "serverOffline": "Server Offline", "yourServers": "Your Coco-Servers", "addServer": "Add New Server"}, "enable_server": "Enable Server", "disable_server": "Disable Server", "manualCopyLink": "If the link did not open automatically, please copy and paste it into your browser manually"}, "tray": {"showCoco": "Show Coco", "settings": "Settings", "quitCoco": "Quit Coco", "checkUpdate": "Check for Updates"}, "update": {"title": "New update available for Coco AI.", "optional_description": "New update available for Coco AI.", "force_description1": "Coco AI update required.", "force_description2": "Please install the latest version to continue.", "releaseNotes": "Release Notes", "button": {"install": "Install", "ok": "Ok"}, "skip_version": "Skip this version", "date": "You're up to date", "latest": "\"{{0}}\" is the latest version"}, "error": {"message": "Sorry, there is an error in your Coco App. Please contact the administrator."}, "history_list": {"search": {"placeholder": "Search"}, "date": {"today": "Today", "yesterday": "Yesterday", "last7Days": "Last 7 Days", "last30Days": "Last 30 Days"}, "menu": {"share": "Share", "rename": "<PERSON><PERSON>", "delete": "Delete"}, "delete_modal": {"title": "Delete chat?", "description": "This will delete \"{{0}}\"", "button": {"delete": "Delete", "cancel": "Cancel"}}}, "calculator": {"sum": "Sum", "subtract": "Difference", "multiply": "Product", "divide": "Divide", "remainder": "<PERSON><PERSON><PERSON>", "expression": "Expression"}, "extensionStore": {"hints": {"installationCompleted": "installation completed", "uninstallationCompleted": "uninstallation completed"}}, "extensionDetail": {"label": {"description": "Description", "commands": "Commands", "tags": "Tags", "lastUpdate": "Last Update"}, "hints": {"installed": "Installed"}, "deleteDialog": {"title": "Uninstall", "description": "This will remove all the data and commands associated with this extension."}}, "deleteDialog": {"button": {"cancel": "Cancel", "delete": "Delete"}}}