// mock
export const res_search = {
  took: 2590,
  timed_out: false,
  _shards: {
    total: 1,
    successful: 1,
    skipped: 0,
    failed: 0
  },
  hits: {
    total: {
      value: 253,
      relation: "eq"
    },
    max_score: 32.709457,
    hits: [
      {
        _index: "coco_document",
        _type: "_doc",
        _id: "3ac857ef30d101b1e5880b53b1438b1a",
        _score: 32.709457,
        _source: {
          icon: "web",
          id: "3ac857ef30d101b1e5880b53b1438b1a",
          source: {
            name: "hugo_site",
            type: "connector"
          },
          type: "web_page",
          category: "Aggregation",
          subcategory: "Metric",
          title: "Avg aggregation",
          content: "",
          author: "liaosy",
          url: "https://pizza.rs/docs/references/aggregation/avg/",
          tags: [
            "avg",
            "aggregation"
          ]
        }
      },
      {
        _index: "coco_document",
        _type: "_doc",
        _id: "2485a744c5dae1278a01c04d39bf60a6",
        _score: 32.37022,
        _source: {
          icon: "web",
          id: "2485a744c5dae1278a01c04d39bf60a6",
          source: {
            name: "hugo_site",
            type: "connector"
          },
          type: "web_page",
          title: "auto_generate_doc_id",
          content: "",
          author: "liaosy",
          url: "https://infinilabs.cn/docs/latest/gateway/references/filters/auto_generate_doc_id/"
        }
      },
      {
        _index: "coco_document",
        _type: "_doc",
        _id: "15aa340fa9ddfcfbf793b8707a4fa16b",
        _score: 21.983166,
        _source: {
          icon: "web",
          id: "15aa340fa9ddfcfbf793b8707a4fa16b",
          source: {
            name: "hugo_site",
            type: "connector"
          },
          type: "web_page",
          category: "Overview",
          subcategory: "Architecture",
          title: "Architecture",
          content: "",
          author: "yangfan",
          url: "https://pizza.rs/docs/overview/architecture/",
          tags: [
            "architecture"
          ]
        }
      },
      {
        _index: "coco_document",
        _type: "_doc",
        _id: "f96b1af318d62a43a44f54731409ff52",
        _score: 21.887964,
        _source: {
          icon: "web",
          id: "f96b1af318d62a43a44f54731409ff52",
          source: {
            name: "hugo_site",
            type: "connector"
          },
          type: "web_page",
          title: "Introducing Coco AI in Two Minutes - A Quick Start Video 🥥",
          content: "",
          author: "yangfan",
          url: "https://blog.infinilabs.com/posts/2024/a-quick-start-viideo-to-introduce-coco-ai-in-two-minutes/",
          tags: [
            "Coco AI",
            "Search",
            "Gen-AI",
            "Enterprise"
          ]
        }
      },
      {
        _index: "coco_document",
        _type: "_doc",
        _id: "3a806937f9e7fe55905a7f71d111e523",
        _score: 21.286049,
        _source: {
          icon: "web",
          id: "3a806937f9e7fe55905a7f71d111e523",
          source: {
            name: "hugo_site",
            type: "connector"
          },
          type: "web_page",
          category: "Catalog",
          subcategory: "Namespace",
          title: "Create a namespace",
          content: "",
          author: "yangfan",
          url: "https://pizza.rs/docs/references/namespace/create/",
          tags: [
            "create",
            "namespace"
          ]
        }
      },
      {
        _index: "coco_document",
        _type: "_doc",
        _id: "04f48643c2c52b872c149e077765f8cb",
        _score: 21.286049,
        _source: {
          icon: "web",
          id: "04f48643c2c52b872c149e077765f8cb",
          source: {
            name: "hugo_site",
            type: "connector"
          },
          type: "web_page",
          category: "Document",
          subcategory: "Index",
          title: "Create a document",
          content: "",
          author: "zouwenan",
          url: "https://pizza.rs/docs/references/document/create/",
          tags: [
            "create",
            "index"
          ]
        }
      },
      {
        _index: "coco_document",
        _type: "_doc",
        _id: "d101818b1e6d2eb23ca2f813ef3a9648",
        _score: 21.213268,
        _source: {
          icon: "web",
          id: "d101818b1e6d2eb23ca2f813ef3a9648",
          source: {
            name: "hugo_site",
            type: "connector"
          },
          type: "web_page",
          category: "Catalog",
          subcategory: "Collection",
          title: "Create a collection",
          content: " ",
          author: "zouwenan",
          url: "https://pizza.rs/docs/references/collection/create/",
          tags: [
            "create",
            "collection"
          ]
        }
      },
      {
        _index: "coco_document",
        _type: "_doc",
        _id: "ee2228755039808c199e6812d09c745e",
        _score: 20.967154,
        _source: {
          icon: "web",
          id: "ee2228755039808c199e6812d09c745e",
          source: {
            name: "hugo_site",
            type: "connector"
          },
          type: "web_page",
          category: "Document",
          subcategory: "Index",
          title: "Delete a document",
          content: "",
          author: "zouwenan",
          url: "https://pizza.rs/docs/references/document/delete/",
          tags: [
            "delete",
            "index"
          ]
        }
      },
      {
        _index: "coco_document",
        _type: "_doc",
        _id: "60584d12aba0ff569e7b79a7be168810",
        _score: 20.967154,
        _source: {
          icon: "web",
          id: "60584d12aba0ff569e7b79a7be168810",
          source: {
            name: "hugo_site",
            type: "connector"
          },
          type: "web_page",
          category: "Catalog",
          subcategory: "Namespace",
          title: "Delete a namespace",
          content: "",
          author: "zouwenan",
          url: "https://pizza.rs/docs/references/namespace/delete/",
          tags: [
            "delete",
            "namespace"
          ]
        }
      },
      {
        _index: "coco_document",
        _type: "_doc",
        _id: "73e31d0feeb8d3d97e4b06a98de54672",
        _score: 20.934437,
        _source: {
          icon: "web",
          id: "73e31d0feeb8d3d97e4b06a98de54672",
          source: {
            name: "hugo_site",
            type: "connector"
          },
          type: "web_page",
          category: "Document",
          subcategory: "Index",
          title: "Replace a document",
          content: " ",
          author: "medcl",
          url: "https://pizza.rs/docs/references/document/replace/",
          tags: [
            "replace",
            "index"
          ]
        }
      }
    ]
  }
}