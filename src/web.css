/* @tailwind base; */
@tailwind components;
@tailwind utilities;

/* Base variables */
:host,
:root,
.searchbox-container,
.coco-container {
  --spacing-base: 12px;
  --modal-width: 560px;
  --modal-height: 600px;
  --searchbox-height: 56px;
  --hit-height: 56px;
  --footer-height: 44px;
  --icon-stroke-width: 1.4;
  --background: #ffffff;
  --foreground: #09090b;
  --border: #e3e3e7;
  --coco-primary-color: rgb(149, 5, 153);
}

/* Light theme */
.light.coco-container{
  --coco-primary-color: rgb(149, 5, 153);
  --coco-text-color: rgb(28, 30, 33);
  --coco-muted-color: rgb(150, 159, 175);
  --coco-modal-container-background: rgba(101, 108, 133, 0.8);
  --coco-modal-background: rgb(245, 246, 247);
  --coco-modal-shadow: inset 1px 1px 0 0 rgba(255, 255, 255, 0.5),
    0 3px 8px 0 rgba(85, 90, 100, 1);
  --coco-searchbox-background: rgb(235, 237, 240);
  --coco-searchbox-focus-background: #fff;
  --coco-hit-color: rgb(68, 73, 80);
  --coco-hit-active-color: #fff;
  --coco-hit-background: #fff;
  --coco-hit-shadow: 0 1px 3px 0 rgb(212, 217, 225);
  --coco-key-gradient: linear-gradient(
    -225deg,
    rgb(213, 219, 228) 0%,
    rgb(248, 248, 248) 100%
  );
  --coco-key-shadow: inset 0 -2px 0 0 rgb(205, 205, 230), inset 0 0 1px 1px #fff,
    0 1px 2px 1px rgba(30, 35, 90, 0.4);
  --coco-footer-background: #fff;
  --coco-footer-shadow: 0 -1px 0 0 rgb(224, 227, 232),
    0 -3px 6px 0 rgba(69, 98, 155, 0.12);
  --coco-icon-color: rgb(21, 21, 21);
}

/* Dark theme */
.dark.coco-container  {
  --coco-primary-color: rgb(149, 5, 153);
  --background: #09090b;
  --foreground: #f9f9f9;
  --border: #27272a;
  --coco-text-color: rgb(245, 246, 247);
  --coco-modal-container-background: rgba(9, 10, 17, 0.8);
  --coco-modal-background: rgb(21, 23, 42);
  --coco-modal-shadow: inset 1px 1px 0 0 rgb(44, 46, 64),
    0 3px 8px 0 rgb(0, 3, 9);
  --coco-searchbox-background: rgb(9, 10, 17);
  --coco-searchbox-focus-background: #000;
  --coco-hit-color: rgb(190, 195, 201);
  --coco-hit-shadow: none;
  --coco-hit-background: rgb(9, 10, 17);
  --coco-key-gradient: linear-gradient(
    -26.5deg,
    rgb(86, 88, 114) 0%,
    rgb(49, 53, 91) 100%
  );
  --coco-key-shadow: inset 0 -2px 0 0 rgb(40, 45, 85),
    inset 0 0 1px 1px rgb(81, 87, 125), 0 2px 2px 0 rgba(3, 4, 9, 0.3);
  --coco-footer-background: rgb(30, 33, 54);
  --coco-footer-shadow: inset 0 1px 0 0 rgba(73, 76, 106, 0.5),
    0 -4px 8px 0 rgba(0, 0, 0, 0.2);
  --coco-muted-color: rgb(127, 132, 151);
  --coco-icon-color: rgb(255, 255, 255);
}

@media (prefers-color-scheme: dark) {
  .dark.coco-container {
    --coco-primary-color: rgb(149, 5, 153);
  --background: #09090b;
  --foreground: #f9f9f9;
  --border: #27272a;
  --coco-text-color: rgb(245, 246, 247);
  --coco-modal-container-background: rgba(9, 10, 17, 0.8);
  --coco-modal-background: rgb(21, 23, 42);
  --coco-modal-shadow: inset 1px 1px 0 0 rgb(44, 46, 64),
    0 3px 8px 0 rgb(0, 3, 9);
  --coco-searchbox-background: rgb(9, 10, 17);
  --coco-searchbox-focus-background: #000;
  --coco-hit-color: rgb(190, 195, 201);
  --coco-hit-shadow: none;
  --coco-hit-background: rgb(9, 10, 17);
  --coco-key-gradient: linear-gradient(
    -26.5deg,
    rgb(86, 88, 114) 0%,
    rgb(49, 53, 91) 100%
  );
  --coco-key-shadow: inset 0 -2px 0 0 rgb(40, 45, 85),
    inset 0 0 1px 1px rgb(81, 87, 125), 0 2px 2px 0 rgba(3, 4, 9, 0.3);
  --coco-footer-background: rgb(30, 33, 54);
  --coco-footer-shadow: inset 0 1px 0 0 rgba(73, 76, 106, 0.5),
    0 -4px 8px 0 rgba(0, 0, 0, 0.2);
  --coco-muted-color: rgb(127, 132, 151);
  --coco-icon-color: rgb(255, 255, 255);
  }
}

/* html,
:host {
  line-height: 1.5;
  -webkit-text-size-adjust: 100%;
  -moz-tab-size: 4;
  -o-tab-size: 4;
  tab-size: 4;
  font-family: ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji",
    "Segoe UI Emoji", Segoe UI Symbol, "Noto Color Emoji";
  font-feature-settings: normal;
  font-variation-settings: normal;
  -webkit-tap-highlight-color: transparent;
} */

/* html {
  height: 100%;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
  overscroll-behavior: none;
}

body,
#root {
  height: 100%;
  --tw-text-opacity: 1;
  color: rgb(17 24 39 / var(--tw-text-opacity, 1));
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
} */

/* body {
  margin: 0;
  line-height: inherit;
} */

.dark body,
.dark #root,
.dark.coco-container,
.dark.coco-container {
  --tw-text-opacity: 1;
  color: rgb(243 244 246 / var(--tw-text-opacity, 1));
}

.input-body {
  overflow: hidden;
  border-radius: 0.375rem;
}

.icon {
  width: 1em;
  height: 1em;
  vertical-align: -0.15em;
  fill: currentColor;
  overflow: hidden;
}

.\!container {
  width: 100% !important;
}
.container {
  width: 100%;
}
@media (min-width: 640px) {
  .\!container {
    max-width: 640px !important;
  }
  .container {
    max-width: 640px;
  }
}
@media (min-width: 768px) {
  .\!container {
    max-width: 768px !important;
  }
  .container {
    max-width: 768px;
  }
}
@media (min-width: 1024px) {
  .\!container {
    max-width: 1024px !important;
  }
  .container {
    max-width: 1024px;
  }
}
@media (min-width: 1280px) {
  .\!container {
    max-width: 1280px !important;
  }
  .container {
    max-width: 1280px;
  }
}
@media (min-width: 1536px) {
  .\!container {
    max-width: 1536px !important;
  }
  .container {
    max-width: 1536px;
  }
}

.coco-container {
  line-height: 1.5;
  -webkit-text-size-adjust: 100%;
  -moz-tab-size: 4;
  -o-tab-size: 4;
  tab-size: 4;
  font-family: ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji",
    "Segoe UI Emoji", Segoe UI Symbol, "Noto Color Emoji";
  font-feature-settings: normal;
  font-variation-settings: normal;
  -webkit-tap-highlight-color: transparent;

  *,
  :before,
  :after {
    --tw-border-spacing-x: 0;
    --tw-border-spacing-y: 0;
    --tw-translate-x: 0;
    --tw-translate-y: 0;
    --tw-rotate: 0;
    --tw-skew-x: 0;
    --tw-skew-y: 0;
    --tw-scale-x: 1;
    --tw-scale-y: 1;
    --tw-pan-x: ;
    --tw-pan-y: ;
    --tw-pinch-zoom: ;
    --tw-scroll-snap-strictness: proximity;
    --tw-gradient-from-position: ;
    --tw-gradient-via-position: ;
    --tw-gradient-to-position: ;
    --tw-ordinal: ;
    --tw-slashed-zero: ;
    --tw-numeric-figure: ;
    --tw-numeric-spacing: ;
    --tw-numeric-fraction: ;
    --tw-ring-inset: ;
    --tw-ring-offset-width: 0px;
    --tw-ring-offset-color: #fff;
    --tw-ring-color: rgb(59 130 246 / 0.5);
    --tw-ring-offset-shadow: 0 0 #0000;
    --tw-ring-shadow: 0 0 #0000;
    --tw-shadow: 0 0 #0000;
    --tw-shadow-colored: 0 0 #0000;
    --tw-blur: ;
    --tw-brightness: ;
    --tw-contrast: ;
    --tw-grayscale: ;
    --tw-hue-rotate: ;
    --tw-invert: ;
    --tw-saturate: ;
    --tw-sepia: ;
    --tw-drop-shadow: ;
    --tw-backdrop-blur: ;
    --tw-backdrop-brightness: ;
    --tw-backdrop-contrast: ;
    --tw-backdrop-grayscale: ;
    --tw-backdrop-hue-rotate: ;
    --tw-backdrop-invert: ;
    --tw-backdrop-opacity: ;
    --tw-backdrop-saturate: ;
    --tw-backdrop-sepia: ;
    --tw-contain-size: ;
    --tw-contain-layout: ;
    --tw-contain-paint: ;
    --tw-contain-style: ;
  }
  ::backdrop {
    --tw-border-spacing-x: 0;
    --tw-border-spacing-y: 0;
    --tw-translate-x: 0;
    --tw-translate-y: 0;
    --tw-rotate: 0;
    --tw-skew-x: 0;
    --tw-skew-y: 0;
    --tw-scale-x: 1;
    --tw-scale-y: 1;
    --tw-pan-x: ;
    --tw-pan-y: ;
    --tw-pinch-zoom: ;
    --tw-scroll-snap-strictness: proximity;
    --tw-gradient-from-position: ;
    --tw-gradient-via-position: ;
    --tw-gradient-to-position: ;
    --tw-ordinal: ;
    --tw-slashed-zero: ;
    --tw-numeric-figure: ;
    --tw-numeric-spacing: ;
    --tw-numeric-fraction: ;
    --tw-ring-inset: ;
    --tw-ring-offset-width: 0px;
    --tw-ring-offset-color: #fff;
    --tw-ring-color: rgb(59 130 246 / 0.5);
    --tw-ring-offset-shadow: 0 0 #0000;
    --tw-ring-shadow: 0 0 #0000;
    --tw-shadow: 0 0 #0000;
    --tw-shadow-colored: 0 0 #0000;
    --tw-blur: ;
    --tw-brightness: ;
    --tw-contrast: ;
    --tw-grayscale: ;
    --tw-hue-rotate: ;
    --tw-invert: ;
    --tw-saturate: ;
    --tw-sepia: ;
    --tw-drop-shadow: ;
    --tw-backdrop-blur: ;
    --tw-backdrop-brightness: ;
    --tw-backdrop-contrast: ;
    --tw-backdrop-grayscale: ;
    --tw-backdrop-hue-rotate: ;
    --tw-backdrop-invert: ;
    --tw-backdrop-opacity: ;
    --tw-backdrop-saturate: ;
    --tw-backdrop-sepia: ;
    --tw-contain-size: ;
    --tw-contain-layout: ;
    --tw-contain-paint: ;
    --tw-contain-style: ;
  }
  *,
  :before,
  :after {
    box-sizing: border-box;
    border-width: 0;
    border-style: solid;
    border-color: #e5e7eb;
  }
  :before,
  :after {
    --tw-content: "";
  }

  hr {
    height: 0;
    color: inherit;
    border-top-width: 1px;
  }
  abbr:where([title]) {
    -webkit-text-decoration: underline dotted;
    text-decoration: underline dotted;
  }
  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    font-size: inherit;
    font-weight: inherit;
  }
  a {
    color: inherit;
    text-decoration: inherit;
  }
  b,
  strong {
    font-weight: bolder;
  }
  code,
  kbd,
  samp,
  pre {
    font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas,
      Liberation Mono, Courier New, monospace;
    font-feature-settings: normal;
    font-variation-settings: normal;
    font-size: 1em;
  }
  small {
    font-size: 80%;
  }
  sub,
  sup {
    font-size: 75%;
    line-height: 0;
    position: relative;
    vertical-align: baseline;
  }
  sub {
    bottom: -0.25em;
  }
  sup {
    top: -0.5em;
  }
  button,
  input,
  optgroup,
  select,
  textarea {
    font-family: inherit;
    font-feature-settings: inherit;
    font-variation-settings: inherit;
    font-size: 100%;
    font-weight: inherit;
    line-height: inherit;
    letter-spacing: inherit;
    color: inherit;
    margin: 0;
    padding: 0;
  }
  button,
  select {
    text-transform: none;
  }
  button,
  input:where([type="button"]),
  input:where([type="reset"]),
  input:where([type="submit"]) {
    -webkit-appearance: button;
    background-color: transparent;
    background-image: none;
  }
  :-moz-focusring {
    outline: auto;
  }
  :-moz-ui-invalid {
    box-shadow: none;
  }
  progress {
    vertical-align: baseline;
  }
  ::-webkit-inner-spin-button,
  ::-webkit-outer-spin-button {
    height: auto;
  }
  [type="search"] {
    -webkit-appearance: textfield;
    outline-offset: -2px;
  }
  ::-webkit-search-decoration {
    -webkit-appearance: none;
  }
  ::-webkit-file-upload-button {
    -webkit-appearance: button;
    font: inherit;
  }
  summary {
    display: list-item;
  }
  blockquote,
  dl,
  dd,
  h1,
  h2,
  h3,
  h4,
  h5,
  h6,
  hr,
  figure,
  p,
  pre {
    margin: inherit;
  }
  fieldset {
    margin: 0;
    padding: 0;
  }
  legend {
    padding: 0;
  }
  ol,
  ul,
  menu {
    list-style: none;
    margin: inherit;
    padding: inherit;
  }
  dialog {
    padding: 0;
  }
  textarea {
    resize: vertical;
  }
  input::-moz-placeholder,
  textarea::-moz-placeholder {
    opacity: 1;
    color: #9ca3af;
  }
  input::placeholder,
  textarea::placeholder {
    opacity: 1;
    color: #9ca3af;
  }
  button,
  [role="button"] {
    cursor: pointer;
  }
  :disabled {
    cursor: default;
  }
  img,
  svg,
  video,
  canvas,
  audio,
  iframe,
  embed,
  object {
    display: block;
    vertical-align: middle;
  }
  img,
  video {
    max-width: 100%;
    height: auto;
  }
  [hidden]:where(:not([hidden="until-found"])) {
    display: none;
  }
  * {
    box-sizing: border-box;
    border-color: var(--border);
  }
  .settings-input {
    display: block;
    width: 100%;
    border-radius: 0.375rem;
    --tw-border-opacity: 1;
    border-color: rgb(209 213 219 / var(--tw-border-opacity, 1));
    --tw-bg-opacity: 1;
    background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
    --tw-text-opacity: 1;
    color: rgb(17 24 39 / var(--tw-text-opacity, 1));
    --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000),
      var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
    transition-property: color, background-color, border-color,
      text-decoration-color, fill, stroke;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 0.2s;
  }
  .settings-input:focus {
    --tw-border-opacity: 1;
    border-color: rgb(59 130 246 / var(--tw-border-opacity, 1));
    --tw-ring-opacity: 1;
    --tw-ring-color: rgb(59 130 246 / var(--tw-ring-opacity, 1));
  }
  .settings-input:is([data-theme="dark"] *) {
    --tw-border-opacity: 1;
    border-color: rgb(75 85 99 / var(--tw-border-opacity, 1));
    --tw-bg-opacity: 1;
    background-color: rgb(55 65 81 / var(--tw-bg-opacity, 1));
    --tw-text-opacity: 1;
    color: rgb(243 244 246 / var(--tw-text-opacity, 1));
  }
  .settings-select {
    border-radius: 0.375rem;
    --tw-border-opacity: 1;
    border-color: rgb(209 213 219 / var(--tw-border-opacity, 1));
    --tw-bg-opacity: 1;
    background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
    font-size: 0.875rem;
    line-height: 1.25rem;
    --tw-text-opacity: 1;
    color: rgb(17 24 39 / var(--tw-text-opacity, 1));
    --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000),
      var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
    transition-property: color, background-color, border-color,
      text-decoration-color, fill, stroke;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 0.2s;
  }
  .settings-select:focus {
    --tw-border-opacity: 1;
    border-color: rgb(59 130 246 / var(--tw-border-opacity, 1));
    --tw-ring-opacity: 1;
    --tw-ring-color: rgb(59 130 246 / var(--tw-ring-opacity, 1));
  }
  .settings-select:is([data-theme="dark"] *) {
    --tw-border-opacity: 1;
    border-color: rgb(75 85 99 / var(--tw-border-opacity, 1));
    --tw-bg-opacity: 1;
    background-color: rgb(55 65 81 / var(--tw-bg-opacity, 1));
    --tw-text-opacity: 1;
    color: rgb(243 244 246 / var(--tw-text-opacity, 1));
  }
}

/* Component styles */
@layer components {
  .settings-input {
    @apply block w-full rounded-md border-gray-300 dark:border-gray-600
        bg-white dark:bg-gray-700
        text-gray-900 dark:text-gray-100
        shadow-sm focus:border-blue-500 focus:ring-blue-500
        transition-colors duration-200;
  }

  .settings-select {
    @apply text-sm rounded-md border-gray-300 dark:border-gray-600
        bg-white dark:bg-gray-700
        text-gray-900 dark:text-gray-100
        shadow-sm focus:border-blue-500 focus:ring-blue-500
        transition-colors duration-200;
  }
}

/* Utility styles */
@layer utilities {
  /* Scrollbar styles */
  .custom-scrollbar {
    scrollbar-width: thin;
    scrollbar-color: #cbd5e1 transparent;
  }

  .custom-scrollbar::-webkit-scrollbar {
    width: 6px;
  }

  .custom-scrollbar::-webkit-scrollbar-track {
    background: transparent;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb {
    background-color: #cbd5e1;
    border-radius: 3px;
  }

  .dark .custom-scrollbar {
    scrollbar-color: #475569 transparent;
  }

  .dark .custom-scrollbar::-webkit-scrollbar-thumb {
    background-color: #475569;
  }

  /* Background styles */
  .bg-100 {
    background-size: 100% 100%;
  }

  /* Error page styles */
  #error-page {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
    background: linear-gradient(to right, #f79c42, #f2d600);
    font-family: "Arial", sans-serif;
    color: #fff;
    text-align: center;
    padding: 0 20px;
  }

  .error-content {
    background-color: rgba(0, 0, 0, 0.6);
    padding: 40px;
    border-radius: 8px;
    box-shadow: 0px 10px 20px rgba(0, 0, 0, 0.2);
    max-width: 500px;
    width: 100%;
    margin: 0 20px;
  }

  .error-title {
    font-size: 60px;
    font-weight: bold;
    margin-bottom: 20px;
    color: #f2d600;
  }

  .error-message {
    font-size: 18px;
    margin-bottom: 20px;
    font-weight: 300;
    color: #fff;
  }

  .error-details {
    font-size: 16px;
    color: #ffcc00;
    font-style: italic;
  }

  .error-content button {
    background-color: #f2d600;
    border: none;
    padding: 10px 20px;
    font-size: 16px;
    color: #333;
    border-radius: 5px;
    cursor: pointer;
    transition: background-color 0.3s ease;
  }

  .error-content button:hover {
    background-color: #f79c42;
  }

  /* coco styles */
  .coco-modal-footer-commands-key {
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    border: 0;
    padding: 2px;
    background: var(--coco-key-gradient);
    box-shadow: var(--coco-key-shadow);
    color: var(--coco-muted-color);
  }

  /* User selection styles */
  .user-select {
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
  }

  .user-select-text {
    -webkit-touch-callout: text;
    -webkit-user-select: text;
    -khtml-user-select: text;
    -moz-user-select: text;
    -ms-user-select: text;
    user-select: text;
  }
}
